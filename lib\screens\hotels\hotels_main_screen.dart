import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/hotels_provider.dart';
import '../../providers/user_provider.dart';
import '../../widgets/hotel_card.dart';
import '../../widgets/search_bar_widget.dart';
import 'hotel_search_screen.dart';
import 'hotel_bookings_screen.dart';

class HotelsMainScreen extends StatefulWidget {
  const HotelsMainScreen({super.key});

  @override
  State<HotelsMainScreen> createState() => _HotelsMainScreenState();
}

class _HotelsMainScreenState extends State<HotelsMainScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HotelsProvider>().loadHotels();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الفنادق'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const HotelSearchScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.bookmark),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const HotelBookingsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: Consumer<HotelsProvider>(
        builder: (context, hotelsProvider, child) {
          if (hotelsProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (hotelsProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red[300],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    hotelsProvider.error!,
                    style: const TextStyle(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      hotelsProvider.loadHotels();
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          final hotels = hotelsProvider.filteredHotels;

          if (hotels.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.hotel_outlined,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'لا توجد فنادق متاحة',
                    style: TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () => hotelsProvider.loadHotels(),
            child: Column(
              children: [
                // شريط البحث السريع
                Container(
                  padding: const EdgeInsets.all(16),
                  child: SearchBarWidget(
                    hintText: 'ابحث عن فندق أو مدينة...',
                    onChanged: (query) {
                      hotelsProvider.searchAndFilter(searchQuery: query);
                    },
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const HotelSearchScreen(),
                        ),
                      );
                    },
                  ),
                ),

                // الفنادق المميزة
                if (hotels.any((hotel) => hotel.isFeatured))
                  Container(
                    height: 200,
                    margin: const EdgeInsets.only(bottom: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16),
                          child: Text(
                            '⭐ فنادق مميزة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Expanded(
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            itemCount: hotels.where((h) => h.isFeatured).length,
                            itemBuilder: (context, index) {
                              final featuredHotels = hotels.where((h) => h.isFeatured).toList();
                              return Container(
                                width: 280,
                                margin: const EdgeInsets.only(left: 12),
                                child: HotelCard(
                                  hotel: featuredHotels[index],
                                  isFeatured: true,
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),

                // جميع الفنادق
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: hotels.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: HotelCard(hotel: hotels[index]),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
