import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/real_estate_provider.dart';
import '../../providers/user_provider.dart';
import '../../models/real_estate.dart';
import '../../widgets/real_estate_card.dart';
import '../../widgets/search_bar_widget.dart';
import 'real_estate_search_screen.dart';
import 'add_property_screen.dart';
import 'liked_properties_screen.dart';

class RealEstateMainScreen extends StatefulWidget {
  const RealEstateMainScreen({super.key});

  @override
  State<RealEstateMainScreen> createState() => _RealEstateMainScreenState();
}

class _RealEstateMainScreenState extends State<RealEstateMainScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<RealEstateProvider>().loadProperties();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('العقارات'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'شراء'),
            Tab(text: 'إيجار'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const RealEstateSearchScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.favorite),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const LikedPropertiesScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddPropertyScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: Consumer<RealEstateProvider>(
        builder: (context, realEstateProvider, child) {
          if (realEstateProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (realEstateProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red[300],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    realEstateProvider.error!,
                    style: const TextStyle(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      realEstateProvider.loadProperties();
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          return TabBarView(
            controller: _tabController,
            children: [
              // تبويب الشراء
              _buildPropertiesTab(
                realEstateProvider,
                RealEstateFilter(listingType: 'شراء'),
                'شراء',
              ),
              // تبويب الإيجار
              _buildPropertiesTab(
                realEstateProvider,
                RealEstateFilter(listingType: 'إيجار'),
                'إيجار',
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPropertiesTab(
    RealEstateProvider provider,
    RealEstateFilter filter,
    String listingType,
  ) {
    // فلترة العقارات حسب نوع الإعلان
    final properties = provider.filteredProperties
        .where((property) => property.listingType == listingType)
        .toList();

    if (properties.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.home_work_outlined,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد عقارات للـ$listingType',
              style: const TextStyle(fontSize: 18, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddPropertyScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.add),
              label: const Text('أضف عقار'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => provider.loadProperties(),
      child: Column(
        children: [
          // شريط البحث السريع
          Container(
            padding: const EdgeInsets.all(16),
            child: SearchBarWidget(
              hintText: 'ابحث عن عقار...',
              onChanged: (query) {
                provider.searchAndFilter(filter, searchQuery: query);
              },
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const RealEstateSearchScreen(),
                  ),
                );
              },
            ),
          ),

          // فلاتر سريعة
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildQuickFilter('سكني', () {
                  provider.searchAndFilter(
                    RealEstateFilter(
                      listingType: listingType,
                      category: 'سكني',
                    ),
                  );
                }),
                const SizedBox(width: 8),
                _buildQuickFilter('تجاري', () {
                  provider.searchAndFilter(
                    RealEstateFilter(
                      listingType: listingType,
                      category: 'تجاري',
                    ),
                  );
                }),
                const SizedBox(width: 8),
                _buildQuickFilter('زراعي', () {
                  provider.searchAndFilter(
                    RealEstateFilter(
                      listingType: listingType,
                      category: 'زراعي',
                    ),
                  );
                }),
                const SizedBox(width: 8),
                _buildQuickFilter('الكل', () {
                  provider.searchAndFilter(
                    RealEstateFilter(listingType: listingType),
                  );
                }),
              ],
            ),
          ),

          const SizedBox(height: 8),

          // العقارات المميزة
          if (properties.any((property) => property.isFeatured))
            Container(
              height: 200,
              margin: const EdgeInsets.only(bottom: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      '⭐ عقارات مميزة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: properties.where((p) => p.isFeatured).length,
                      itemBuilder: (context, index) {
                        final featuredProperties = properties.where((p) => p.isFeatured).toList();
                        return Container(
                          width: 280,
                          margin: const EdgeInsets.only(left: 12),
                          child: RealEstateCard(
                            property: featuredProperties[index],
                            isFeatured: true,
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),

          // جميع العقارات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: properties.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: RealEstateCard(property: properties[index]),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickFilter(String title, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.blue[50],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.blue[200]!),
        ),
        child: Text(
          title,
          style: TextStyle(
            color: Colors.blue[700],
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
