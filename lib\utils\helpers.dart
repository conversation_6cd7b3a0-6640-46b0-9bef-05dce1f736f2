import 'dart:math';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'constants.dart';

class AppHelpers {
  // تنسيق التاريخ
  static String formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy', 'ar').format(date);
  }

  static String formatDateTime(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy HH:mm', 'ar').format(dateTime);
  }

  static String formatTime(DateTime time) {
    return DateFormat('HH:mm', 'ar').format(time);
  }

  static String formatDateRange(DateTime start, DateTime end) {
    return '${formatDate(start)} - ${formatDate(end)}';
  }

  // تنسيق الأرقام
  static String formatPrice(double price) {
    final formatter = NumberFormat('#,##0', 'ar');
    return '${formatter.format(price)} ${AppStrings.currency}';
  }

  static String formatArea(double area) {
    final formatter = NumberFormat('#,##0.#', 'ar');
    return '${formatter.format(area)} ${AppStrings.areaUnit}';
  }

  static String formatDistance(double distance) {
    if (distance < 1) {
      return '${(distance * 1000).round()} م';
    }
    return '${distance.toStringAsFixed(1)} ${AppStrings.distanceUnit}';
  }

  static String formatDuration(int minutes) {
    if (minutes < 60) {
      return '$minutes ${AppStrings.timeUnit}';
    }
    final hours = minutes ~/ 60;
    final remainingMinutes = minutes % 60;
    if (remainingMinutes == 0) {
      return '$hours ساعة';
    }
    return '$hours ساعة و $remainingMinutes ${AppStrings.timeUnit}';
  }

  // حساب المسافة بين نقطتين
  static double calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    const double earthRadius = AppConstants.earthRadiusKm;
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);

    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);

    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return earthRadius * c;
  }

  static double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  // حساب سعر التكسي
  static double calculateTaxiPrice(double distance, String rideType) {
    final double basePrice = AppConstants.baseTaxiPrice;
    final double pricePerKm = AppConstants.pricePerKm;
    final double multiplier = AppConstants.rideTypeMultipliers[rideType] ?? 1.0;

    return (basePrice + (distance * pricePerKm)) * multiplier;
  }

  // حساب الوقت التقديري للرحلة
  static int calculateTravelTime(double distance) {
    final double timeInHours = distance / AppConstants.averageSpeedKmh;
    return (timeInHours * 60).round(); // تحويل إلى دقائق
  }

  // التحقق من صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // التحقق من صحة رقم الهاتف السعودي
  static bool isValidSaudiPhone(String phone) {
    // إزالة المسافات والرموز
    phone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    // التحقق من الأنماط المختلفة
    return RegExp(r'^(\+966|966|0)?5[0-9]{8}$').hasMatch(phone);
  }

  // تنسيق رقم الهاتف
  static String formatPhoneNumber(String phone) {
    phone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    if (phone.startsWith('+966')) {
      return phone;
    } else if (phone.startsWith('966')) {
      return '+$phone';
    } else if (phone.startsWith('05')) {
      return '+966${phone.substring(1)}';
    } else if (phone.startsWith('5')) {
      return '+966$phone';
    }
    
    return phone;
  }

  // حساب عدد الليالي
  static int calculateNights(DateTime checkIn, DateTime checkOut) {
    return checkOut.difference(checkIn).inDays;
  }

  // حساب العمر
  static int calculateAge(DateTime birthDate) {
    final DateTime now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month ||
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  // تحويل النص إلى عنوان URL صالح
  static String slugify(String text) {
    return text
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s-]'), '')
        .replaceAll(RegExp(r'[-\s]+'), '-')
        .trim();
  }

  // اختصار النص
  static String truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }

  // تحويل الوقت النسبي (منذ كم من الوقت)
  static String timeAgo(DateTime dateTime) {
    final Duration difference = DateTime.now().difference(dateTime);
    
    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return 'منذ $years ${years == 1 ? 'سنة' : 'سنوات'}';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return 'منذ $months ${months == 1 ? 'شهر' : 'أشهر'}';
    } else if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  // التحقق من قوة كلمة المرور
  static PasswordStrength checkPasswordStrength(String password) {
    if (password.length < 6) return PasswordStrength.weak;
    
    bool hasUppercase = password.contains(RegExp(r'[A-Z]'));
    bool hasLowercase = password.contains(RegExp(r'[a-z]'));
    bool hasDigits = password.contains(RegExp(r'[0-9]'));
    bool hasSpecialCharacters = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    
    int score = 0;
    if (hasUppercase) score++;
    if (hasLowercase) score++;
    if (hasDigits) score++;
    if (hasSpecialCharacters) score++;
    if (password.length >= 8) score++;
    
    if (score < 2) return PasswordStrength.weak;
    if (score < 4) return PasswordStrength.medium;
    return PasswordStrength.strong;
  }

  // إنشاء لون عشوائي
  static Color generateRandomColor() {
    final Random random = Random();
    return Color.fromRGBO(
      random.nextInt(256),
      random.nextInt(256),
      random.nextInt(256),
      1.0,
    );
  }

  // تحويل HEX إلى Color
  static Color hexToColor(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  // تحويل Color إلى HEX
  static String colorToHex(Color color) {
    return '#${color.value.toRadixString(16).substring(2)}';
  }

  // التحقق من الاتصال بالإنترنت
  static Future<bool> hasInternetConnection() async {
    try {
      // يمكن تحسين هذا باستخدام مكتبة connectivity_plus
      return true; // مؤقت
    } catch (e) {
      return false;
    }
  }

  // إنشاء ID فريد
  static String generateUniqueId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  // تحويل الحجم بالبايت إلى نص قابل للقراءة
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  // التحقق من نوع الملف
  static bool isImageFile(String fileName) {
    final String extension = fileName.split('.').last.toLowerCase();
    return AppConstants.allowedImageFormats.contains(extension);
  }

  // إنشاء نص عشوائي
  static String generateRandomString(int length) {
    const String chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final Random random = Random();
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => chars.codeUnitAt(random.nextInt(chars.length)),
      ),
    );
  }

  // تحويل الرقم إلى نص عربي
  static String numberToArabicText(int number) {
    // تنفيذ مبسط - يمكن تحسينه
    final Map<int, String> arabicNumbers = {
      0: 'صفر',
      1: 'واحد',
      2: 'اثنان',
      3: 'ثلاثة',
      4: 'أربعة',
      5: 'خمسة',
      6: 'ستة',
      7: 'سبعة',
      8: 'ثمانية',
      9: 'تسعة',
      10: 'عشرة',
    };
    
    return arabicNumbers[number] ?? number.toString();
  }
}

enum PasswordStrength {
  weak,
  medium,
  strong,
}

extension PasswordStrengthExtension on PasswordStrength {
  String get text {
    switch (this) {
      case PasswordStrength.weak:
        return 'ضعيفة';
      case PasswordStrength.medium:
        return 'متوسطة';
      case PasswordStrength.strong:
        return 'قوية';
    }
  }

  Color get color {
    switch (this) {
      case PasswordStrength.weak:
        return Colors.red;
      case PasswordStrength.medium:
        return Colors.orange;
      case PasswordStrength.strong:
        return Colors.green;
    }
  }
}
