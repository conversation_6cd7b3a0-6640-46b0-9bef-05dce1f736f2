@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🚀 تطبيق الخدمات المتعددة
echo ========================================
echo.

echo 📋 التحقق من متطلبات Flutter...
flutter doctor -v

echo.
echo 📦 تثبيت المكتبات المطلوبة...
flutter pub get

echo.
echo 🧹 تنظيف المشروع...
flutter clean

echo.
echo 📦 إعادة تثبيت المكتبات...
flutter pub get

echo.
echo 🔧 إنشاء ملفات الكود المطلوبة...
flutter packages pub run build_runner build --delete-conflicting-outputs

echo.
echo ========================================
echo    اختر منصة التشغيل:
echo ========================================
echo 1. Android (الموبايل)
echo 2. Web (المتصفح)
echo 3. Windows (سطح المكتب)
echo 4. إظهار الأجهزة المتاحة
echo 5. خروج
echo.

set /p choice="أدخل اختيارك (1-5): "

if "%choice%"=="1" goto android
if "%choice%"=="2" goto web
if "%choice%"=="3" goto windows
if "%choice%"=="4" goto devices
if "%choice%"=="5" goto exit

echo ❌ اختيار غير صحيح!
pause
goto start

:android
echo.
echo 📱 تشغيل التطبيق على Android...
flutter run -d android
goto end

:web
echo.
echo 🌐 تشغيل التطبيق على الويب...
echo 🔗 سيتم فتح التطبيق على: http://localhost:8080
flutter run -d web-server --web-port 8080 --web-hostname 0.0.0.0
goto end

:windows
echo.
echo 💻 تشغيل التطبيق على Windows...
flutter run -d windows
goto end

:devices
echo.
echo 📱 الأجهزة المتاحة:
flutter devices
echo.
pause
goto start

:exit
echo.
echo 👋 شكراً لاستخدام تطبيق الخدمات المتعددة!
exit

:end
echo.
echo ✅ تم إنهاء التطبيق
pause
