import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/user_provider.dart';
import '../widgets/loading_widget.dart';
import 'hotels/hotels_main_screen.dart';
import 'real_estate/real_estate_main_screen.dart';
import 'taxi/taxi_main_screen.dart';
import 'profile/profile_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with TickerProviderStateMixin {
  int _currentIndex = 0;
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<Widget> _screens = [
    const HotelsMainScreen(),
    const RealEstateMainScreen(),
    const TaxiMainScreen(),
    const ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        return Scaffold(
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              children: _screens,
            ),
          ),
          bottomNavigationBar: Container(
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: BottomNavigationBar(
              currentIndex: _currentIndex,
              onTap: _onTabTapped,
              type: BottomNavigationBarType.fixed,
              elevation: 0,
              selectedFontSize: 12,
              unselectedFontSize: 10,
              items: [
                _buildBottomNavItem(
                  icon: Icons.hotel_outlined,
                  activeIcon: Icons.hotel,
                  label: 'الفنادق',
                  index: 0,
                ),
                _buildBottomNavItem(
                  icon: Icons.home_work_outlined,
                  activeIcon: Icons.home_work,
                  label: 'العقارات',
                  index: 1,
                ),
                _buildBottomNavItem(
                  icon: Icons.local_taxi_outlined,
                  activeIcon: Icons.local_taxi,
                  label: 'التكسي',
                  index: 2,
                ),
                _buildBottomNavItem(
                  icon: Icons.person_outline,
                  activeIcon: Icons.person,
                  label: 'الملف الشخصي',
                  index: 3,
                  showBadge: userProvider.hasUnreadNotifications,
                ),
              ],
            ),
          ),
          floatingActionButton: _buildFloatingActionButton(),
        );
      },
    );
  }

  BottomNavigationBarItem _buildBottomNavItem({
    required IconData icon,
    required IconData activeIcon,
    required String label,
    required int index,
    bool showBadge = false,
  }) {
    final isActive = _currentIndex == index;

    return BottomNavigationBarItem(
      icon: Stack(
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isActive
                  ? Theme.of(context).primaryColor.withOpacity(0.1)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              isActive ? activeIcon : icon,
              size: isActive ? 26 : 24,
            ),
          ),
          if (showBadge)
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
            ),
        ],
      ),
      label: label,
    );
  }

  Widget? _buildFloatingActionButton() {
    // إظهار زر عائم فقط في شاشة التكسي
    if (_currentIndex == 2) {
      return FloatingActionButton(
        onPressed: () {
          // TODO: فتح شاشة طلب رحلة سريعة
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('طلب رحلة سريعة')),
          );
        },
        child: const Icon(Icons.add),
        tooltip: 'طلب رحلة',
      );
    }
    return null;
  }

  void _onTabTapped(int index) {
    if (index == _currentIndex) {
      // إذا تم النقر على نفس التبويب، قم بالتمرير للأعلى
      _scrollToTop(index);
    } else {
      setState(() {
        _currentIndex = index;
      });
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _scrollToTop(int index) {
    // TODO: تنفيذ التمرير للأعلى في كل شاشة
    // يمكن استخدام ScrollController أو GlobalKey للوصول لكل شاشة
  }
}
