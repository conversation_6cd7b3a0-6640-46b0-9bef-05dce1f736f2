import 'package:flutter/material.dart';
import 'hotels/hotels_main_screen.dart';
import 'real_estate/real_estate_main_screen.dart';
import 'taxi/taxi_main_screen.dart';
import 'profile/profile_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;
  
  final List<Widget> _screens = [
    const HotelsMainScreen(),
    const RealEstateMainScreen(),
    const TaxiMainScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.hotel),
            activeIcon: Icon(Icons.hotel, size: 28),
            label: 'الفنادق',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.home_work),
            activeIcon: Icon(Icons.home_work, size: 28),
            label: 'العقارات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.local_taxi),
            activeIcon: Icon(Icons.local_taxi, size: 28),
            label: 'التكسي',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            activeIcon: Icon(Icons.person, size: 28),
            label: 'الملف الشخصي',
          ),
        ],
      ),
    );
  }
}
