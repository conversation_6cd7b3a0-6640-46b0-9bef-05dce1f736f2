import 'package:flutter/material.dart';
import '../models/real_estate.dart';

class RealEstateProvider with ChangeNotifier {
  List<RealEstate> _properties = [];
  List<RealEstate> _filteredProperties = [];
  List<RealEstate> _likedProperties = [];
  List<RealEstateLike> _likes = [];
  bool _isLoading = false;
  String? _error;

  List<RealEstate> get properties => _properties;
  List<RealEstate> get filteredProperties => _filteredProperties;
  List<RealEstate> get likedProperties => _likedProperties;
  bool get isLoading => _isLoading;
  String? get error => _error;

  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // تحميل العقارات
  Future<void> loadProperties() async {
    try {
      setLoading(true);
      setError(null);

      // محاكاة API call
      await Future.delayed(const Duration(seconds: 2));

      // بيانات تجريبية
      _properties = [
        RealEstate(
          id: '1',
          title: 'فيلا فاخرة للبيع',
          description: 'فيلا مودرن بتصميم عصري في حي راقي',
          address: 'الرياض، حي النرجس',
          latitude: 24.7136,
          longitude: 46.6753,
          images: [
            'https://example.com/villa1_1.jpg',
            'https://example.com/villa1_2.jpg',
          ],
          price: 1250000.0,
          listingType: 'شراء',
          category: 'سكني',
          subCategory: 'طابو',
          area: 400.0,
          bedrooms: 5,
          bathrooms: 4,
          contactPhone: '+966501234567',
          contactName: 'أحمد السعد',
          userId: '1',
          likesCount: 15,
          isFeatured: true,
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
        ),
        RealEstate(
          id: '2',
          title: 'شقة للإيجار - موقع مميز',
          description: 'شقة مفروشة بالكامل في برج سكني حديث',
          address: 'جدة، حي الزهراء',
          latitude: 21.3891,
          longitude: 39.8579,
          images: [
            'https://example.com/apartment1_1.jpg',
            'https://example.com/apartment1_2.jpg',
          ],
          price: 3500.0,
          listingType: 'إيجار',
          category: 'سكني',
          area: 120.0,
          bedrooms: 2,
          bathrooms: 2,
          contactPhone: '+966502345678',
          contactName: 'فاطمة أحمد',
          userId: '2',
          likesCount: 8,
          createdAt: DateTime.now().subtract(const Duration(days: 5)),
        ),
        RealEstate(
          id: '3',
          title: 'أرض زراعية للبيع',
          description: 'أرض زراعية خصبة مع بئر ماء',
          address: 'القصيم، بريدة',
          latitude: 26.3260,
          longitude: 43.9750,
          images: [
            'https://example.com/land1_1.jpg',
          ],
          price: 850000.0,
          listingType: 'شراء',
          category: 'زراعي',
          subCategory: 'زراعي',
          area: 5000.0,
          contactPhone: '+966503456789',
          contactName: 'محمد العتيبي',
          userId: '3',
          likesCount: 3,
          createdAt: DateTime.now().subtract(const Duration(days: 20)),
        ),
        RealEstate(
          id: '4',
          title: 'محل تجاري للإيجار',
          description: 'محل في موقع تجاري حيوي',
          address: 'الدمام، شارع الملك فهد',
          latitude: 26.4207,
          longitude: 50.0888,
          images: [
            'https://example.com/shop1_1.jpg',
          ],
          price: 8000.0,
          listingType: 'إيجار',
          category: 'تجاري',
          area: 80.0,
          contactPhone: '+966504567890',
          contactName: 'خالد الشمري',
          userId: '4',
          likesCount: 12,
          createdAt: DateTime.now().subtract(const Duration(days: 3)),
        ),
      ];

      _filteredProperties = List.from(_properties);
      setLoading(false);
    } catch (e) {
      setError('خطأ في تحميل العقارات: ${e.toString()}');
      setLoading(false);
    }
  }

  // البحث والفلترة
  void searchAndFilter(RealEstateFilter filter, {String? searchQuery}) {
    _filteredProperties = _properties.where((property) {
      bool matches = true;

      if (searchQuery != null && searchQuery.isNotEmpty) {
        matches = matches && 
            (property.title.toLowerCase().contains(searchQuery.toLowerCase()) ||
             property.address.toLowerCase().contains(searchQuery.toLowerCase()) ||
             property.description.toLowerCase().contains(searchQuery.toLowerCase()));
      }

      if (filter.listingType != null) {
        matches = matches && property.listingType == filter.listingType;
      }

      if (filter.category != null) {
        matches = matches && property.category == filter.category;
      }

      if (filter.subCategory != null) {
        matches = matches && property.subCategory == filter.subCategory;
      }

      if (filter.minPrice != null) {
        matches = matches && property.price >= filter.minPrice!;
      }

      if (filter.maxPrice != null) {
        matches = matches && property.price <= filter.maxPrice!;
      }

      if (filter.minArea != null) {
        matches = matches && property.area >= filter.minArea!;
      }

      if (filter.maxArea != null) {
        matches = matches && property.area <= filter.maxArea!;
      }

      if (filter.minBedrooms != null && property.bedrooms != null) {
        matches = matches && property.bedrooms! >= filter.minBedrooms!;
      }

      if (filter.maxBedrooms != null && property.bedrooms != null) {
        matches = matches && property.bedrooms! <= filter.maxBedrooms!;
      }

      if (filter.city != null) {
        matches = matches && property.address.toLowerCase().contains(filter.city!.toLowerCase());
      }

      return matches;
    }).toList();

    // ترتيب العقارات المميزة أولاً
    _filteredProperties.sort((a, b) {
      if (a.isFeatured && !b.isFeatured) return -1;
      if (!a.isFeatured && b.isFeatured) return 1;
      return b.createdAt.compareTo(a.createdAt);
    });

    notifyListeners();
  }

  // إضافة/إزالة إعجاب
  Future<bool> toggleLike(String propertyId, String userId) async {
    try {
      final existingLike = _likes.firstWhere(
        (like) => like.realEstateId == propertyId && like.userId == userId,
        orElse: () => RealEstateLike(id: '', realEstateId: '', userId: '', createdAt: DateTime.now()),
      );

      if (existingLike.id.isNotEmpty) {
        // إزالة الإعجاب
        _likes.removeWhere((like) => like.id == existingLike.id);
        
        // تحديث عدد الإعجابات
        final propertyIndex = _properties.indexWhere((p) => p.id == propertyId);
        if (propertyIndex != -1) {
          final property = _properties[propertyIndex];
          _properties[propertyIndex] = RealEstate(
            id: property.id,
            title: property.title,
            description: property.description,
            address: property.address,
            latitude: property.latitude,
            longitude: property.longitude,
            images: property.images,
            price: property.price,
            listingType: property.listingType,
            category: property.category,
            subCategory: property.subCategory,
            area: property.area,
            bedrooms: property.bedrooms,
            bathrooms: property.bathrooms,
            contactPhone: property.contactPhone,
            contactName: property.contactName,
            userId: property.userId,
            likesCount: property.likesCount - 1,
            isFeatured: property.isFeatured,
            createdAt: property.createdAt,
          );
        }
      } else {
        // إضافة إعجاب
        final newLike = RealEstateLike(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          realEstateId: propertyId,
          userId: userId,
          createdAt: DateTime.now(),
        );
        _likes.add(newLike);

        // تحديث عدد الإعجابات
        final propertyIndex = _properties.indexWhere((p) => p.id == propertyId);
        if (propertyIndex != -1) {
          final property = _properties[propertyIndex];
          _properties[propertyIndex] = RealEstate(
            id: property.id,
            title: property.title,
            description: property.description,
            address: property.address,
            latitude: property.latitude,
            longitude: property.longitude,
            images: property.images,
            price: property.price,
            listingType: property.listingType,
            category: property.category,
            subCategory: property.subCategory,
            area: property.area,
            bedrooms: property.bedrooms,
            bathrooms: property.bathrooms,
            contactPhone: property.contactPhone,
            contactName: property.contactName,
            userId: property.userId,
            likesCount: property.likesCount + 1,
            isFeatured: property.isFeatured,
            createdAt: property.createdAt,
          );
        }
      }

      // تحديث قائمة العقارات المفضلة
      _likedProperties = _properties.where((property) => 
          _likes.any((like) => like.realEstateId == property.id && like.userId == userId)
      ).toList();

      notifyListeners();
      return true;
    } catch (e) {
      setError('خطأ في تحديث الإعجاب: ${e.toString()}');
      return false;
    }
  }

  bool isLiked(String propertyId, String userId) {
    return _likes.any((like) => like.realEstateId == propertyId && like.userId == userId);
  }

  // إضافة عقار جديد
  Future<bool> addProperty(RealEstate property) async {
    try {
      setLoading(true);
      setError(null);

      // محاكاة API call
      await Future.delayed(const Duration(seconds: 2));

      _properties.add(property);
      _filteredProperties = List.from(_properties);

      setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      setError('خطأ في إضافة العقار: ${e.toString()}');
      setLoading(false);
      return false;
    }
  }

  // تحميل العقارات المفضلة
  void loadLikedProperties(String userId) {
    _likedProperties = _properties.where((property) => 
        _likes.any((like) => like.realEstateId == property.id && like.userId == userId)
    ).toList();
    notifyListeners();
  }
}
