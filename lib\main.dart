import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'screens/main_screen.dart';
import 'providers/hotels_provider.dart';
import 'providers/real_estate_provider.dart';
import 'providers/taxi_provider.dart';
import 'providers/user_provider.dart';
import 'utils/app_theme.dart';

void main() {
  runApp(const MultiServiceApp());
}

class MultiServiceApp extends StatelessWidget {
  const MultiServiceApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => UserProvider()),
        ChangeNotifierProvider(create: (_) => HotelsProvider()),
        ChangeNotifierProvider(create: (_) => RealEstateProvider()),
        ChangeNotifierProvider(create: (_) => TaxiProvider()),
      ],
      child: MaterialApp(
        title: 'تطبيق الخدمات المتعددة',
        debugShowCheckedModeBanner: false,
        
        // Arabic Localization
        locale: const Locale('ar', 'SA'),
        supportedLocales: const [
          Locale('ar', 'SA'),
          Locale('en', 'US'),
        ],
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        
        // Theme
        theme: AppTheme.lightTheme,
        
        home: const MainScreen(),
      ),
    );
  }
}
