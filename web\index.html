<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="تطبيق الخدمات المتعددة - فنادق، عقارات، تكسي">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="تطبيق الخدمات المتعددة">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>تطبيق الخدمات المتعددة</title>
  <link rel="manifest" href="manifest.json">

  <script>
    // The value below is injected by flutter build, do not touch.
    var serviceWorkerVersion = null;
  </script>
  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js" defer></script>

  <style>
    body {
      font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      direction: rtl;
    }

    .loading-container {
      text-align: center;
      color: white;
      padding: 2rem;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .app-title {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 0.5rem;
    }

    .app-subtitle {
      font-size: 1rem;
      opacity: 0.8;
      margin-bottom: 2rem;
    }

    .features {
      display: flex;
      gap: 2rem;
      justify-content: center;
      flex-wrap: wrap;
      margin-top: 2rem;
    }

    .feature {
      background: rgba(255, 255, 255, 0.1);
      padding: 1rem;
      border-radius: 10px;
      backdrop-filter: blur(10px);
      min-width: 120px;
      text-align: center;
    }

    .feature-icon {
      font-size: 2rem;
      margin-bottom: 0.5rem;
    }

    .feature-text {
      font-size: 0.9rem;
    }

    @media (max-width: 768px) {
      .features {
        flex-direction: column;
        align-items: center;
      }
      
      .app-title {
        font-size: 1.5rem;
      }
    }
  </style>
</head>
<body>
  <div class="loading-container" id="loading">
    <div class="loading-spinner"></div>
    <div class="app-title">تطبيق الخدمات المتعددة</div>
    <div class="app-subtitle">فنادق • عقارات • تكسي</div>
    <div>جاري التحميل...</div>
    
    <div class="features">
      <div class="feature">
        <div class="feature-icon">🏨</div>
        <div class="feature-text">حجز الفنادق</div>
      </div>
      <div class="feature">
        <div class="feature-icon">🏠</div>
        <div class="feature-text">العقارات</div>
      </div>
      <div class="feature">
        <div class="feature-icon">🚕</div>
        <div class="feature-text">خدمة التكسي</div>
      </div>
    </div>
  </div>

  <script>
    window.addEventListener('load', function(ev) {
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        onEntrypointLoaded: function(engineInitializer) {
          engineInitializer.initializeEngine().then(function(appRunner) {
            // Hide loading screen
            document.getElementById('loading').style.display = 'none';
            appRunner.runApp();
          });
        }
      });
    });
  </script>
</body>
</html>
