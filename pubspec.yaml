name: multi_service_app
description: تطبيق خدمات متعددة - فنادق، عقارات، تكسي
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Navigation
  cupertino_icons: ^1.0.6
  flutter_localizations:
    sdk: flutter
  intl: ^0.18.1
  
  # Maps (Free OpenStreetMap)
  flutter_map: ^6.1.0
  latlong2: ^0.8.1
  
  # HTTP & API
  http: ^1.1.0
  dio: ^5.3.2
  
  # State Management
  provider: ^6.1.1
  
  # Local Storage
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0
  
  # Location & GPS
  geolocator: ^10.1.0
  geocoding: ^2.1.1
  
  # Image & Media
  image_picker: ^1.0.4
  cached_network_image: ^3.3.0
  
  # Utils
  uuid: ^4.1.0
  url_launcher: ^6.2.1
  permission_handler: ^11.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
  
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
