import 'package:flutter/material.dart';
import '../../widgets/message_widgets.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<NotificationItem> _notifications = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadNotifications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadNotifications() {
    // بيانات تجريبية للإشعارات
    _notifications = [
      NotificationItem(
        id: '1',
        title: 'تم تأكيد حجز الفندق',
        message: 'تم تأكيد حجزك في فندق الريتز كارلتون للفترة من 15-20 ديسمبر',
        type: NotificationType.booking,
        timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
        isRead: false,
        icon: Icons.hotel,
        color: Colors.blue,
      ),
      NotificationItem(
        id: '2',
        title: 'عقار جديد مطابق لبحثك',
        message: 'تم إضافة شقة للإيجار في الرياض تطابق معايير البحث الخاصة بك',
        type: NotificationType.realEstate,
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        isRead: false,
        icon: Icons.home,
        color: Colors.green,
      ),
      NotificationItem(
        id: '3',
        title: 'وصل السائق',
        message: 'السائق أحمد وصل إلى موقعك. رقم السيارة: أ ب ج 1234',
        type: NotificationType.taxi,
        timestamp: DateTime.now().subtract(const Duration(hours: 5)),
        isRead: true,
        icon: Icons.local_taxi,
        color: Colors.orange,
      ),
      NotificationItem(
        id: '4',
        title: 'تقييم الرحلة',
        message: 'كيف كانت رحلتك مع السائق أحمد؟ شاركنا تقييمك',
        type: NotificationType.taxi,
        timestamp: DateTime.now().subtract(const Duration(hours: 6)),
        isRead: true,
        icon: Icons.star_rate,
        color: Colors.amber,
      ),
      NotificationItem(
        id: '5',
        title: 'عرض خاص على الفنادق',
        message: 'خصم 25% على جميع الفنادق في جدة لفترة محدودة',
        type: NotificationType.promotion,
        timestamp: DateTime.now().subtract(const Duration(days: 1)),
        isRead: true,
        icon: Icons.local_offer,
        color: Colors.red,
      ),
      NotificationItem(
        id: '6',
        title: 'تحديث التطبيق',
        message: 'إصدار جديد من التطبيق متاح الآن مع مميزات محسنة',
        type: NotificationType.system,
        timestamp: DateTime.now().subtract(const Duration(days: 2)),
        isRead: true,
        icon: Icons.system_update,
        color: Colors.purple,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإشعارات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.mark_email_read),
            onPressed: _markAllAsRead,
            tooltip: 'تحديد الكل كمقروء',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'clear_all':
                  _clearAllNotifications();
                  break;
                case 'settings':
                  _openNotificationSettings();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'clear_all',
                child: Row(
                  children: [
                    Icon(Icons.clear_all),
                    SizedBox(width: 8),
                    Text('مسح الكل'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: 8),
                    Text('إعدادات الإشعارات'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(
              text: 'الكل (${_notifications.length})',
            ),
            Tab(
              text: 'الفنادق (${_notifications.where((n) => n.type == NotificationType.booking).length})',
            ),
            Tab(
              text: 'العقارات (${_notifications.where((n) => n.type == NotificationType.realEstate).length})',
            ),
            Tab(
              text: 'التكسي (${_notifications.where((n) => n.type == NotificationType.taxi).length})',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildNotificationsList(_notifications),
          _buildNotificationsList(_notifications.where((n) => n.type == NotificationType.booking).toList()),
          _buildNotificationsList(_notifications.where((n) => n.type == NotificationType.realEstate).toList()),
          _buildNotificationsList(_notifications.where((n) => n.type == NotificationType.taxi).toList()),
        ],
      ),
    );
  }

  Widget _buildNotificationsList(List<NotificationItem> notifications) {
    if (notifications.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.notifications_none,
        title: 'لا توجد إشعارات',
        subtitle: 'ستظهر الإشعارات الجديدة هنا',
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await Future.delayed(const Duration(seconds: 1));
        setState(() {
          _loadNotifications();
        });
      },
      child: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: notifications.length,
        separatorBuilder: (context, index) => const SizedBox(height: 8),
        itemBuilder: (context, index) {
          final notification = notifications[index];
          return _buildNotificationCard(notification);
        },
      ),
    );
  }

  Widget _buildNotificationCard(NotificationItem notification) {
    return Dismissible(
      key: Key(notification.id),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Icon(
          Icons.delete,
          color: Colors.white,
        ),
      ),
      onDismissed: (direction) {
        setState(() {
          _notifications.removeWhere((n) => n.id == notification.id);
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('تم حذف الإشعار'),
            action: SnackBarAction(
              label: 'تراجع',
              onPressed: () {
                setState(() {
                  _notifications.add(notification);
                  _notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));
                });
              },
            ),
          ),
        );
      },
      child: Card(
        elevation: notification.isRead ? 2 : 4,
        child: InkWell(
          onTap: () => _markAsRead(notification),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: notification.isRead ? null : Colors.blue[50],
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: notification.color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Icon(
                    notification.icon,
                    color: notification.color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              notification.title,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: notification.isRead 
                                    ? FontWeight.w500 
                                    : FontWeight.bold,
                              ),
                            ),
                          ),
                          if (!notification.isRead)
                            Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: Colors.blue,
                                shape: BoxShape.circle,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        notification.message,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _formatTimestamp(notification.timestamp),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  void _markAsRead(NotificationItem notification) {
    if (!notification.isRead) {
      setState(() {
        notification.isRead = true;
      });
    }
  }

  void _markAllAsRead() {
    setState(() {
      for (var notification in _notifications) {
        notification.isRead = true;
      }
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحديد جميع الإشعارات كمقروءة')),
    );
  }

  void _clearAllNotifications() {
    ConfirmationDialog.show(
      context,
      title: 'مسح جميع الإشعارات',
      message: 'هل أنت متأكد من رغبتك في مسح جميع الإشعارات؟',
      confirmText: 'مسح',
      confirmColor: Colors.red,
      onConfirm: () {
        setState(() {
          _notifications.clear();
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم مسح جميع الإشعارات')),
        );
      },
    );
  }

  void _openNotificationSettings() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات الإشعارات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('إشعارات الحجوزات'),
              subtitle: const Text('تلقي إشعارات حول حالة الحجوزات'),
              value: true,
              onChanged: (value) {},
            ),
            SwitchListTile(
              title: const Text('إشعارات العقارات'),
              subtitle: const Text('تلقي إشعارات حول العقارات الجديدة'),
              value: true,
              onChanged: (value) {},
            ),
            SwitchListTile(
              title: const Text('إشعارات التكسي'),
              subtitle: const Text('تلقي إشعارات حول حالة الرحلات'),
              value: true,
              onChanged: (value) {},
            ),
            SwitchListTile(
              title: const Text('العروض والخصومات'),
              subtitle: const Text('تلقي إشعارات حول العروض الخاصة'),
              value: false,
              onChanged: (value) {},
            ),
          ],
        ),
      ),
    );
  }
}

class NotificationItem {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime timestamp;
  bool isRead;
  final IconData icon;
  final Color color;

  NotificationItem({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    required this.isRead,
    required this.icon,
    required this.color,
  });
}

enum NotificationType {
  booking,
  realEstate,
  taxi,
  promotion,
  system,
}
