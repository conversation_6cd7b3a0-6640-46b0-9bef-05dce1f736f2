# دليل إعداد وتشغيل تطبيق الخدمات المتعددة

## المتطلبات الأساسية

### 1. تثبيت Flutter
```bash
# تحميل Flutter SDK من الموقع الرسمي
https://flutter.dev/docs/get-started/install

# التحقق من التثبيت
flutter doctor
```

### 2. إعداد بيئة التطوير
- **Android Studio** أو **VS Code**
- **Android SDK** (للتطوير على Android)
- **Xcode** (للتطوير على iOS - Mac فقط)

## خطوات التثبيت

### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd multi_service_app
```

### 2. تثبيت المكتبات
```bash
flutter pub get
```

### 3. إعداد الأذونات

#### Android
تم إعداد الأذونات في `android/app/src/main/AndroidManifest.xml`:
- إذن الإنترنت
- إذن الموقع (دقيق وتقريبي)
- إذن الكاميرا
- إذن قراءة وكتابة الملفات
- إذن الاتصال

#### iOS (إضافي)
أضف الأذونات التالية في `ios/Runner/Info.plist`:
```xml
<key>NSLocationWhenInUseUsageDescription</key>
<string>يحتاج التطبيق للموقع لتحديد موقعك وإيجاد الخدمات القريبة</string>

<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>يحتاج التطبيق للموقع لتتبع الرحلات</string>

<key>NSCameraUsageDescription</key>
<string>يحتاج التطبيق للكاميرا لالتقاط صور العقارات</string>

<key>NSPhotoLibraryUsageDescription</key>
<string>يحتاج التطبيق لمعرض الصور لاختيار صور العقارات</string>
```

## تشغيل التطبيق

### 1. تشغيل على المحاكي/الجهاز
```bash
# تشغيل على جهاز متصل أو محاكي
flutter run

# تشغيل في وضع التطوير مع Hot Reload
flutter run --debug

# تشغيل في وضع الإنتاج
flutter run --release
```

### 2. بناء التطبيق للإنتاج

#### Android APK
```bash
flutter build apk --release
```

#### Android App Bundle
```bash
flutter build appbundle --release
```

#### iOS
```bash
flutter build ios --release
```

## إعداد الخرائط

التطبيق يستخدم **OpenStreetMap** كخرائط مجانية عبر مكتبة `flutter_map`.

### مميزات الخرائط المجانية:
- لا تحتاج API key
- لا توجد حدود استخدام
- تعمل بدون تكلفة

### إعدادات الخرائط في `lib/utils/constants.dart`:
```dart
static const String openStreetMapUrl = 'https://tile.openstreetmap.org/{z}/{x}/{y}.png';
static const double defaultLatitude = 24.7136; // الرياض
static const double defaultLongitude = 46.6753;
```

## البيانات التجريبية

### حساب تجريبي:
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456

### بيانات تجريبية متاحة:
- فنادق تجريبية في الرياض وجدة
- عقارات تجريبية بفئات مختلفة
- سائقين تجريبيين للتكسي

## هيكل المشروع

```
lib/
├── main.dart                 # نقطة البداية
├── models/                   # نماذج البيانات
│   ├── user.dart            # نموذج المستخدم
│   ├── hotel.dart           # نموذج الفندق
│   ├── real_estate.dart     # نموذج العقار
│   └── taxi.dart            # نموذج التكسي
├── providers/               # إدارة الحالة
│   ├── user_provider.dart
│   ├── hotels_provider.dart
│   ├── real_estate_provider.dart
│   └── taxi_provider.dart
├── screens/                 # الشاشات
│   ├── main_screen.dart     # الشاشة الرئيسية
│   ├── hotels/              # شاشات الفنادق
│   ├── real_estate/         # شاشات العقارات
│   ├── taxi/                # شاشات التكسي
│   └── profile/             # شاشات الملف الشخصي
├── widgets/                 # المكونات المشتركة
├── utils/                   # المساعدات والثوابت
│   ├── constants.dart       # الثوابت
│   ├── helpers.dart         # الدوال المساعدة
│   └── app_theme.dart       # الثيمات والألوان
└── assets/                  # الملفات الثابتة
    ├── images/
    ├── icons/
    └── fonts/
```

## المكتبات المستخدمة

### الأساسية:
- `flutter`: إطار العمل
- `provider`: إدارة الحالة
- `flutter_localizations`: الترجمة والتوطين

### الخرائط والموقع:
- `flutter_map`: عرض الخرائط المجانية
- `latlong2`: التعامل مع الإحداثيات
- `geolocator`: تحديد الموقع
- `geocoding`: تحويل الإحداثيات لعناوين

### الشبكة والبيانات:
- `http`: طلبات HTTP
- `dio`: عميل HTTP متقدم
- `shared_preferences`: تخزين محلي
- `sqflite`: قاعدة بيانات محلية

### الوسائط:
- `image_picker`: اختيار الصور
- `cached_network_image`: تخزين الصور مؤقتاً

### المساعدة:
- `uuid`: إنشاء معرفات فريدة
- `url_launcher`: فتح الروابط
- `permission_handler`: إدارة الأذونات

## استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ في الأذونات
```bash
# تأكد من إضافة الأذونات في AndroidManifest.xml
# تأكد من طلب الأذونات في الكود
```

#### 2. مشاكل الخرائط
```bash
# تأكد من الاتصال بالإنترنت
# تحقق من إعدادات الشبكة
```

#### 3. مشاكل الموقع
```bash
# تأكد من تفعيل GPS
# تأكد من منح إذن الموقع
```

#### 4. مشاكل البناء
```bash
# نظف المشروع
flutter clean
flutter pub get

# أعد بناء المشروع
flutter build apk
```

## التطوير والمساهمة

### إضافة ميزة جديدة:
1. أنشئ branch جديد
2. أضف الميزة مع التوثيق
3. اختبر الميزة
4. أرسل Pull Request

### معايير الكود:
- استخدم أسماء واضحة للمتغيرات والدوال
- أضف تعليقات للكود المعقد
- اتبع نمط التسمية المتفق عليه
- اختبر الكود قبل الإرسال

## النشر

### Android:
1. قم ببناء App Bundle
2. ارفع على Google Play Console
3. اتبع إرشادات Google Play

### iOS:
1. قم ببناء الأرشيف
2. ارفع على App Store Connect
3. اتبع إرشادات App Store

## الدعم

للحصول على المساعدة:
- راجع الوثائق في README.md
- تحقق من Issues في GitHub
- تواصل مع فريق التطوير

---

**ملاحظة**: هذا التطبيق في مرحلة التطوير ويستخدم بيانات تجريبية. للاستخدام التجاري، يرجى إعداد قاعدة بيانات حقيقية وخدمات API.
