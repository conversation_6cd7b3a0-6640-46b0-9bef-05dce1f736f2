import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/hotels_provider.dart';
import '../../widgets/hotel_card.dart';
import '../../widgets/search_bar_widget.dart';

class HotelSearchScreen extends StatefulWidget {
  const HotelSearchScreen({super.key});

  @override
  State<HotelSearchScreen> createState() => _HotelSearchScreenState();
}

class _HotelSearchScreenState extends State<HotelSearchScreen> {
  final _searchController = TextEditingController();
  
  // فلاتر البحث
  int? _selectedMinStars;
  int? _selectedMaxStars;
  double? _selectedMinPrice;
  double? _selectedMaxPrice;
  String? _selectedHotelType;
  int? _selectedMinGuests;

  final List<String> _hotelTypes = ['فندق', 'شقق', 'منتجع', 'بيت ضيافة'];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _applyFilters() {
    context.read<HotelsProvider>().searchAndFilter(
      searchQuery: _searchController.text,
      minStars: _selectedMinStars,
      maxStars: _selectedMaxStars,
      minPrice: _selectedMinPrice,
      maxPrice: _selectedMaxPrice,
      hotelType: _selectedHotelType,
      minGuests: _selectedMinGuests,
    );
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _selectedMinStars = null;
      _selectedMaxStars = null;
      _selectedMinPrice = null;
      _selectedMaxPrice = null;
      _selectedHotelType = null;
      _selectedMinGuests = null;
    });
    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('البحث في الفنادق'),
        actions: [
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: _clearFilters,
            tooltip: 'مسح الفلاتر',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلاتر
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[50],
            child: Column(
              children: [
                // شريط البحث
                SearchBarWidget(
                  controller: _searchController,
                  hintText: 'ابحث عن فندق أو مدينة...',
                  onChanged: (query) {
                    _applyFilters();
                  },
                ),

                const SizedBox(height: 16),

                // فلاتر سريعة
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip(
                        'النجوم',
                        _selectedMinStars != null || _selectedMaxStars != null,
                        () => _showStarsFilter(),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'السعر',
                        _selectedMinPrice != null || _selectedMaxPrice != null,
                        () => _showPriceFilter(),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'نوع الفندق',
                        _selectedHotelType != null,
                        () => _showHotelTypeFilter(),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'عدد الضيوف',
                        _selectedMinGuests != null,
                        () => _showGuestsFilter(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // النتائج
          Expanded(
            child: Consumer<HotelsProvider>(
              builder: (context, hotelsProvider, child) {
                if (hotelsProvider.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                final hotels = hotelsProvider.filteredHotels;

                if (hotels.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد نتائج',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'جرب تعديل معايير البحث',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: hotels.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: HotelCard(hotel: hotels[index]),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isActive, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive ? Colors.blue : Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isActive ? Colors.blue : Colors.grey[300]!,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: TextStyle(
                color: isActive ? Colors.white : Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
            ),
            if (isActive) ...[
              const SizedBox(width: 4),
              Icon(
                Icons.check,
                size: 16,
                color: Colors.white,
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showStarsFilter() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تصنيف النجوم',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                const Text('من: '),
                Expanded(
                  child: DropdownButton<int>(
                    value: _selectedMinStars,
                    hint: const Text('اختر'),
                    isExpanded: true,
                    items: [1, 2, 3, 4, 5].map((stars) {
                      return DropdownMenuItem(
                        value: stars,
                        child: Row(
                          children: List.generate(stars, (index) => 
                            const Icon(Icons.star, color: Colors.amber, size: 16)
                          ),
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedMinStars = value;
                      });
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  child: const Text('تطبيق'),
                ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedMinStars = null;
                      _selectedMaxStars = null;
                    });
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  child: const Text('مسح'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showPriceFilter() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نطاق السعر (ر.س)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      labelText: 'السعر الأدنى',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      _selectedMinPrice = double.tryParse(value);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      labelText: 'السعر الأعلى',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      _selectedMaxPrice = double.tryParse(value);
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  child: const Text('تطبيق'),
                ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedMinPrice = null;
                      _selectedMaxPrice = null;
                    });
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  child: const Text('مسح'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showHotelTypeFilter() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نوع الفندق',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            ..._hotelTypes.map((type) => RadioListTile<String>(
              title: Text(type),
              value: type,
              groupValue: _selectedHotelType,
              onChanged: (value) {
                setState(() {
                  _selectedHotelType = value;
                });
                Navigator.pop(context);
                _applyFilters();
              },
            )),
            
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedHotelType = null;
                });
                Navigator.pop(context);
                _applyFilters();
              },
              child: const Text('مسح الاختيار'),
            ),
          ],
        ),
      ),
    );
  }

  void _showGuestsFilter() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'عدد الضيوف',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            DropdownButton<int>(
              value: _selectedMinGuests,
              hint: const Text('اختر عدد الضيوف'),
              isExpanded: true,
              items: [1, 2, 3, 4, 5, 6].map((guests) {
                return DropdownMenuItem(
                  value: guests,
                  child: Text('$guests ${guests == 1 ? 'ضيف' : 'ضيوف'}'),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedMinGuests = value;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  child: const Text('تطبيق'),
                ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedMinGuests = null;
                    });
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  child: const Text('مسح'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
