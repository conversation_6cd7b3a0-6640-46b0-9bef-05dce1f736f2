<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق الخدمات المتعددة - عرض تجريبي</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .services {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .service-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .service-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            display: block;
        }

        .service-card h3 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            color: #fff;
        }

        .service-features {
            list-style: none;
            text-align: right;
            margin-top: 1.5rem;
        }

        .service-features li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .service-features li:last-child {
            border-bottom: none;
        }

        .tech-features {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .tech-features h3 {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 2rem;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .tech-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            font-weight: 600;
        }

        .demo-section {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin: 2rem 0;
        }

        .demo-section h3 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
        }

        .demo-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .demo-step {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 10px;
            border-right: 4px solid #FFC107;
        }

        .demo-step h4 {
            color: #FFC107;
            margin-bottom: 0.5rem;
        }

        .code-block {
            background: rgba(0, 0, 0, 0.4);
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            text-align: left;
            direction: ltr;
            overflow-x: auto;
        }

        .footer {
            text-align: center;
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            opacity: 0.8;
        }

        .btn {
            display: inline-block;
            background: #FFC107;
            color: #333;
            padding: 1rem 2rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            margin: 1rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #FFD54F;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4);
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .services {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 1rem;
            }
        }

        .floating-icons {
            position: fixed;
            top: 50%;
            left: 2rem;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 1rem;
            z-index: 1000;
        }

        .floating-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .floating-icon:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .floating-icons {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="floating-icons">
        <div class="floating-icon" title="الفنادق">🏨</div>
        <div class="floating-icon" title="العقارات">🏠</div>
        <div class="floating-icon" title="التكسي">🚕</div>
    </div>

    <div class="container">
        <header class="header">
            <h1>📱 تطبيق الخدمات المتعددة</h1>
            <p>تطبيق Flutter شامل يجمع بين خدمات الفنادق والعقارات والتكسي في مكان واحد مع واجهة عربية كاملة</p>
        </header>

        <div class="services">
            <div class="service-card">
                <span class="service-icon">🏨</span>
                <h3>قسم الفنادق</h3>
                <p>نظام شامل لحجز الفنادق مع بحث متقدم وإدارة الحجوزات</p>
                <ul class="service-features">
                    <li>🔍 بحث وفلترة متقدمة حسب النجوم والسعر والموقع</li>
                    <li>⭐ عرض التقييمات والمراجعات</li>
                    <li>📅 نظام حجز متكامل مع تحديد التواريخ</li>
                    <li>❤️ قائمة المفضلة لحفظ الفنادق المميزة</li>
                    <li>🏆 قسم خاص للفنادق المميزة</li>
                    <li>📱 إدارة الحجوزات (حالية، سابقة، ملغية)</li>
                </ul>
            </div>

            <div class="service-card">
                <span class="service-icon">🏠</span>
                <h3>قسم العقارات</h3>
                <p>منصة متكاملة للعقارات مع إمكانية البيع والإيجار</p>
                <ul class="service-features">
                    <li>🏘️ عقارات للبيع والإيجار</li>
                    <li>🏢 فئات متعددة: سكني، تجاري، زراعي</li>
                    <li>👍 نظام الإعجاب مع عداد التفاعل</li>
                    <li>➕ إمكانية إضافة عقارات جديدة</li>
                    <li>📱 معلومات تواصل مباشرة مع المالك</li>
                    <li>🔍 بحث متقدم بفلاتر متعددة</li>
                </ul>
            </div>

            <div class="service-card">
                <span class="service-icon">🚕</span>
                <h3>قسم التكسي</h3>
                <p>خدمة طلب التكسي مع تتبع مباشر وخرائط مجانية</p>
                <ul class="service-features">
                    <li>🗺️ خرائط مجانية باستخدام OpenStreetMap</li>
                    <li>📍 تحديد الموقع التلقائي مع GPS</li>
                    <li>🚗 أنواع رحلات متعددة (فردية، جماعية، فاخرة)</li>
                    <li>💰 حساب السعر والمسافة والوقت التقديري</li>
                    <li>📊 تاريخ الرحلات والتقييمات</li>
                    <li>🔄 تتبع مباشر للرحلة</li>
                </ul>
            </div>
        </div>

        <div class="tech-features">
            <h3>🛠️ المميزات التقنية</h3>
            <div class="tech-grid">
                <div class="tech-item">🌐 دعم الويب والموبايل</div>
                <div class="tech-item">🇸🇦 واجهة عربية كاملة</div>
                <div class="tech-item">🗺️ خرائط مجانية (OpenStreetMap)</div>
                <div class="tech-item">📱 تصميم متجاوب</div>
                <div class="tech-item">⚡ أداء عالي مع Flutter</div>
                <div class="tech-item">🔄 إدارة حالة متقدمة (Provider)</div>
                <div class="tech-item">💾 تخزين محلي للبيانات</div>
                <div class="tech-item">🎨 ثيم موحد وألوان متناسقة</div>
            </div>
        </div>

        <div class="demo-section">
            <h3>🚀 كيفية تشغيل التطبيق</h3>
            <div class="demo-steps">
                <div class="demo-step">
                    <h4>الخطوة 1</h4>
                    <p>تثبيت Flutter SDK من الموقع الرسمي</p>
                </div>
                <div class="demo-step">
                    <h4>الخطوة 2</h4>
                    <p>استنساخ المشروع وتثبيت المكتبات</p>
                </div>
                <div class="demo-step">
                    <h4>الخطوة 3</h4>
                    <p>تشغيل التطبيق على الويب أو الموبايل</p>
                </div>
            </div>

            <div class="code-block">
# استنساخ المشروع
git clone [repository-url]
cd multi_service_app

# تثبيت المكتبات
flutter pub get

# تشغيل على الويب
flutter run -d web-server --web-port 8080

# تشغيل على الموبايل
flutter run
            </div>

            <div style="margin-top: 2rem;">
                <a href="#" class="btn">📥 تحميل الكود المصدري</a>
                <a href="#" class="btn">📖 قراءة الوثائق</a>
            </div>
        </div>

        <div class="tech-features">
            <h3>📋 بنية المشروع</h3>
            <div class="code-block">
lib/
├── main.dart                 # نقطة البداية
├── models/                   # نماذج البيانات
│   ├── user.dart            # نموذج المستخدم
│   ├── hotel.dart           # نموذج الفندق
│   ├── real_estate.dart     # نموذج العقار
│   └── taxi.dart            # نموذج التكسي
├── providers/               # إدارة الحالة
│   ├── user_provider.dart
│   ├── hotels_provider.dart
│   ├── real_estate_provider.dart
│   └── taxi_provider.dart
├── screens/                 # الشاشات
│   ├── main_screen.dart     # الشاشة الرئيسية
│   ├── hotels/              # شاشات الفنادق
│   ├── real_estate/         # شاشات العقارات
│   ├── taxi/                # شاشات التكسي
│   └── profile/             # شاشات الملف الشخصي
├── widgets/                 # المكونات المشتركة
└── utils/                   # المساعدات والثوابت
            </div>
        </div>

        <footer class="footer">
            <p>تم تطوير هذا التطبيق باستخدام Flutter مع دعم كامل للغة العربية</p>
            <p>جميع الحقوق محفوظة © 2024</p>
        </footer>
    </div>

    <script>
        // إضافة تأثيرات تفاعلية
        document.querySelectorAll('.service-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // تأثير النقر على الأيقونات العائمة
        document.querySelectorAll('.floating-icon').forEach(icon => {
            icon.addEventListener('click', function() {
                const service = this.getAttribute('title');
                alert(`سيتم فتح قسم ${service} في التطبيق الكامل!`);
            });
        });

        // تأثير التمرير
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.header');
            const speed = scrolled * 0.5;
            parallax.style.transform = `translateY(${speed}px)`;
        });
    </script>
</body>
</html>
