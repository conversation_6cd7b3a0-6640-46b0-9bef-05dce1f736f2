class RealEstate {
  final String id;
  final String title;
  final String description;
  final String address;
  final double latitude;
  final double longitude;
  final List<String> images;
  final double price;
  final String listingType; // شراء، إيجار
  final String category; // سكني، تجاري، زراعي
  final String subCategory; // طابو، زراعي (للشراء فقط)
  final double area; // المساحة
  final int? bedrooms; // عدد الغرف (للسكني)
  final int? bathrooms; // عدد الحمامات (للسكني)
  final String contactPhone;
  final String contactName;
  final String userId;
  final int likesCount;
  final bool isFeatured;
  final DateTime createdAt;

  RealEstate({
    required this.id,
    required this.title,
    required this.description,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.images,
    required this.price,
    required this.listingType,
    required this.category,
    this.subCategory = '',
    required this.area,
    this.bedrooms,
    this.bathrooms,
    required this.contactPhone,
    required this.contactName,
    required this.userId,
    this.likesCount = 0,
    this.isFeatured = false,
    required this.createdAt,
  });

  factory RealEstate.fromJson(Map<String, dynamic> json) {
    return RealEstate(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      address: json['address'],
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      images: List<String>.from(json['images']),
      price: json['price'].toDouble(),
      listingType: json['listingType'],
      category: json['category'],
      subCategory: json['subCategory'] ?? '',
      area: json['area'].toDouble(),
      bedrooms: json['bedrooms'],
      bathrooms: json['bathrooms'],
      contactPhone: json['contactPhone'],
      contactName: json['contactName'],
      userId: json['userId'],
      likesCount: json['likesCount'] ?? 0,
      isFeatured: json['isFeatured'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'images': images,
      'price': price,
      'listingType': listingType,
      'category': category,
      'subCategory': subCategory,
      'area': area,
      'bedrooms': bedrooms,
      'bathrooms': bathrooms,
      'contactPhone': contactPhone,
      'contactName': contactName,
      'userId': userId,
      'likesCount': likesCount,
      'isFeatured': isFeatured,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class RealEstateLike {
  final String id;
  final String realEstateId;
  final String userId;
  final DateTime createdAt;

  RealEstateLike({
    required this.id,
    required this.realEstateId,
    required this.userId,
    required this.createdAt,
  });

  factory RealEstateLike.fromJson(Map<String, dynamic> json) {
    return RealEstateLike(
      id: json['id'],
      realEstateId: json['realEstateId'],
      userId: json['userId'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'realEstateId': realEstateId,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

// فلاتر البحث
class RealEstateFilter {
  final String? listingType;
  final String? category;
  final String? subCategory;
  final double? minPrice;
  final double? maxPrice;
  final double? minArea;
  final double? maxArea;
  final int? minBedrooms;
  final int? maxBedrooms;
  final String? city;

  RealEstateFilter({
    this.listingType,
    this.category,
    this.subCategory,
    this.minPrice,
    this.maxPrice,
    this.minArea,
    this.maxArea,
    this.minBedrooms,
    this.maxBedrooms,
    this.city,
  });

  Map<String, dynamic> toJson() {
    return {
      'listingType': listingType,
      'category': category,
      'subCategory': subCategory,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'minArea': minArea,
      'maxArea': maxArea,
      'minBedrooms': minBedrooms,
      'maxBedrooms': maxBedrooms,
      'city': city,
    };
  }
}
