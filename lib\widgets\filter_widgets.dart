import 'package:flutter/material.dart';

class FilterChip extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback onTap;
  final IconData? icon;
  final Color? selectedColor;

  const FilterChip({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onTap,
    this.icon,
    this.selectedColor,
  });

  @override
  Widget build(BuildContext context) {
    final color = selectedColor ?? Theme.of(context).primaryColor;
    
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: 1.5,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: color.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                size: 16,
                color: isSelected ? Colors.white : Colors.grey[600],
              ),
              const SizedBox(width: 6),
            ],
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey[700],
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                fontSize: 14,
              ),
            ),
            if (isSelected) ...[
              const SizedBox(width: 6),
              Icon(
                Icons.check,
                size: 16,
                color: Colors.white,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class PriceRangeFilter extends StatefulWidget {
  final double minPrice;
  final double maxPrice;
  final double currentMin;
  final double currentMax;
  final Function(double, double)? onChanged;
  final String currency;

  const PriceRangeFilter({
    super.key,
    required this.minPrice,
    required this.maxPrice,
    required this.currentMin,
    required this.currentMax,
    this.onChanged,
    this.currency = 'ر.س',
  });

  @override
  State<PriceRangeFilter> createState() => _PriceRangeFilterState();
}

class _PriceRangeFilterState extends State<PriceRangeFilter> {
  late RangeValues _currentRangeValues;

  @override
  void initState() {
    super.initState();
    _currentRangeValues = RangeValues(widget.currentMin, widget.currentMax);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نطاق السعر',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        // عرض القيم الحالية
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '${_currentRangeValues.start.round()} ${widget.currency}',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ),
            const Text(' - '),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '${_currentRangeValues.end.round()} ${widget.currency}',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        // شريط التمرير
        RangeSlider(
          values: _currentRangeValues,
          min: widget.minPrice,
          max: widget.maxPrice,
          divisions: 20,
          labels: RangeLabels(
            '${_currentRangeValues.start.round()} ${widget.currency}',
            '${_currentRangeValues.end.round()} ${widget.currency}',
          ),
          onChanged: (RangeValues values) {
            setState(() {
              _currentRangeValues = values;
            });
            widget.onChanged?.call(values.start, values.end);
          },
        ),
      ],
    );
  }
}

class MultiSelectFilter extends StatefulWidget {
  final String title;
  final List<String> options;
  final List<String> selectedOptions;
  final Function(List<String>)? onChanged;
  final int maxSelections;

  const MultiSelectFilter({
    super.key,
    required this.title,
    required this.options,
    required this.selectedOptions,
    this.onChanged,
    this.maxSelections = -1, // -1 means no limit
  });

  @override
  State<MultiSelectFilter> createState() => _MultiSelectFilterState();
}

class _MultiSelectFilterState extends State<MultiSelectFilter> {
  late List<String> _selectedOptions;

  @override
  void initState() {
    super.initState();
    _selectedOptions = List.from(widget.selectedOptions);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: widget.options.map((option) {
            final isSelected = _selectedOptions.contains(option);
            final canSelect = widget.maxSelections == -1 || 
                _selectedOptions.length < widget.maxSelections || 
                isSelected;
            
            return FilterChip(
              label: option,
              isSelected: isSelected,
              onTap: canSelect ? () {
                setState(() {
                  if (isSelected) {
                    _selectedOptions.remove(option);
                  } else {
                    _selectedOptions.add(option);
                  }
                });
                widget.onChanged?.call(_selectedOptions);
              } : () {},
            );
          }).toList(),
        ),
        
        if (widget.maxSelections > 0 && _selectedOptions.length >= widget.maxSelections)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'يمكنك اختيار ${widget.maxSelections} خيارات كحد أقصى',
              style: TextStyle(
                fontSize: 12,
                color: Colors.orange[700],
              ),
            ),
          ),
      ],
    );
  }
}

class SortFilter extends StatelessWidget {
  final String currentSort;
  final List<SortOption> options;
  final Function(String)? onChanged;

  const SortFilter({
    super.key,
    required this.currentSort,
    required this.options,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'ترتيب حسب',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        
        ...options.map((option) => RadioListTile<String>(
          title: Row(
            children: [
              if (option.icon != null) ...[
                Icon(option.icon, size: 20),
                const SizedBox(width: 8),
              ],
              Text(option.label),
            ],
          ),
          value: option.value,
          groupValue: currentSort,
          onChanged: (value) {
            if (value != null) {
              onChanged?.call(value);
            }
          },
          contentPadding: EdgeInsets.zero,
        )),
      ],
    );
  }
}

class SortOption {
  final String value;
  final String label;
  final IconData? icon;

  const SortOption({
    required this.value,
    required this.label,
    this.icon,
  });
}

class FilterBottomSheet extends StatefulWidget {
  final Widget child;
  final VoidCallback? onApply;
  final VoidCallback? onClear;
  final String title;

  const FilterBottomSheet({
    super.key,
    required this.child,
    this.onApply,
    this.onClear,
    this.title = 'الفلاتر',
  });

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.title,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (widget.onClear != null)
                  TextButton(
                    onPressed: widget.onClear,
                    child: const Text('مسح الكل'),
                  ),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          // Content
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: widget.child,
            ),
          ),
          
          // Actions
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(
                top: BorderSide(color: Colors.grey[200]!),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      widget.onApply?.call();
                      Navigator.pop(context);
                    },
                    child: const Text('تطبيق'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static void show(
    BuildContext context, {
    required Widget child,
    VoidCallback? onApply,
    VoidCallback? onClear,
    String title = 'الفلاتر',
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FilterBottomSheet(
        title: title,
        onApply: onApply,
        onClear: onClear,
        child: child,
      ),
    );
  }
}

class QuickFilters extends StatelessWidget {
  final List<QuickFilter> filters;
  final Function(String)? onFilterTap;

  const QuickFilters({
    super.key,
    required this.filters,
    this.onFilterTap,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: filters.map((filter) => Padding(
          padding: const EdgeInsets.only(left: 8),
          child: FilterChip(
            label: filter.label,
            isSelected: filter.isSelected,
            icon: filter.icon,
            onTap: () => onFilterTap?.call(filter.value),
          ),
        )).toList(),
      ),
    );
  }
}

class QuickFilter {
  final String value;
  final String label;
  final bool isSelected;
  final IconData? icon;

  const QuickFilter({
    required this.value,
    required this.label,
    required this.isSelected,
    this.icon,
  });
}
