class Hotel {
  final String id;
  final String name;
  final String description;
  final String address;
  final double latitude;
  final double longitude;
  final List<String> images;
  final int stars;
  final double rating;
  final int reviewsCount;
  final String type; // فندق، شقق، منتجع، بيت ضيافة
  final List<Room> rooms;
  final List<String> amenities;
  final bool isFeatured;
  final DateTime createdAt;

  Hotel({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.images,
    required this.stars,
    required this.rating,
    required this.reviewsCount,
    required this.type,
    required this.rooms,
    required this.amenities,
    this.isFeatured = false,
    required this.createdAt,
  });

  factory Hotel.fromJson(Map<String, dynamic> json) {
    return Hotel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      address: json['address'],
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      images: List<String>.from(json['images']),
      stars: json['stars'],
      rating: json['rating'].toDouble(),
      reviewsCount: json['reviewsCount'],
      type: json['type'],
      rooms: (json['rooms'] as List).map((room) => Room.fromJson(room)).toList(),
      amenities: List<String>.from(json['amenities']),
      isFeatured: json['isFeatured'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'images': images,
      'stars': stars,
      'rating': rating,
      'reviewsCount': reviewsCount,
      'type': type,
      'rooms': rooms.map((room) => room.toJson()).toList(),
      'amenities': amenities,
      'isFeatured': isFeatured,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class Room {
  final String id;
  final String type;
  final double price;
  final int maxGuests;
  final List<String> amenities;
  final bool isAvailable;

  Room({
    required this.id,
    required this.type,
    required this.price,
    required this.maxGuests,
    required this.amenities,
    this.isAvailable = true,
  });

  factory Room.fromJson(Map<String, dynamic> json) {
    return Room(
      id: json['id'],
      type: json['type'],
      price: json['price'].toDouble(),
      maxGuests: json['maxGuests'],
      amenities: List<String>.from(json['amenities']),
      isAvailable: json['isAvailable'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'price': price,
      'maxGuests': maxGuests,
      'amenities': amenities,
      'isAvailable': isAvailable,
    };
  }
}

class HotelBooking {
  final String id;
  final String hotelId;
  final String roomId;
  final String userId;
  final DateTime checkIn;
  final DateTime checkOut;
  final int guests;
  final double totalPrice;
  final String status; // pending, confirmed, cancelled
  final DateTime createdAt;

  HotelBooking({
    required this.id,
    required this.hotelId,
    required this.roomId,
    required this.userId,
    required this.checkIn,
    required this.checkOut,
    required this.guests,
    required this.totalPrice,
    required this.status,
    required this.createdAt,
  });

  factory HotelBooking.fromJson(Map<String, dynamic> json) {
    return HotelBooking(
      id: json['id'],
      hotelId: json['hotelId'],
      roomId: json['roomId'],
      userId: json['userId'],
      checkIn: DateTime.parse(json['checkIn']),
      checkOut: DateTime.parse(json['checkOut']),
      guests: json['guests'],
      totalPrice: json['totalPrice'].toDouble(),
      status: json['status'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'hotelId': hotelId,
      'roomId': roomId,
      'userId': userId,
      'checkIn': checkIn.toIso8601String(),
      'checkOut': checkOut.toIso8601String(),
      'guests': guests,
      'totalPrice': totalPrice,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}
