# 📱 تطبيق الخدمات المتعددة - الإصدار المحسن

<div align="center">

![Flutter](https://img.shields.io/badge/Flutter-3.10+-blue.svg)
![Dart](https://img.shields.io/badge/Dart-3.0+-blue.svg)
![License](https://img.shields.io/badge/License-MIT-green.svg)
![Platform](https://img.shields.io/badge/Platform-Android%20%7C%20iOS%20%7C%20Web-lightgrey.svg)

**تطبيق Flutter شامل ومتطور يجمع بين خدمات الفنادق والعقارات والتكسي في مكان واحد مع واجهة عربية كاملة وتصميم عصري**

[المميزات](#-المميزات-الرئيسية) • [التثبيت](#-التثبيت-والتشغيل) • [الاستخدام](#-كيفية-الاستخدام) • [المساهمة](#-المساهمة)

</div>

---

## 🌟 المميزات الرئيسية

### 🏨 **قسم الفنادق المتطور**
- 🔍 **بحث ذكي متقدم** مع فلاتر حسب النجوم والسعر والموقع
- ⭐ **نظام تقييم تفاعلي** بالنجوم مع مراجعات المستخدمين
- 📅 **نظام حجز متكامل** مع تحديد التواريخ وإدارة الحجوزات
- ❤️ **قائمة المفضلة الذكية** لحفظ الفنادق المميزة
- 🏆 **قسم الفنادق المميزة** مع عروض خاصة
- 📱 **إدارة شاملة للحجوزات** (حالية، سابقة، ملغية)

### 🏠 **قسم العقارات الشامل**
- 🏘️ **عقارات متنوعة** للبيع والإيجار
- 🏢 **فئات متعددة** (سكني، تجاري، زراعي، استثماري)
- 👍 **نظام إعجاب تفاعلي** مع عداد التفاعل
- ➕ **إضافة عقارات سهلة** مع معرض صور متقدم
- 📱 **تواصل مباشر** مع أصحاب العقارات
- 🔍 **بحث متقدم** بفلاتر ذكية ومتعددة

### 🚕 **قسم التكسي المتطور**
- 🗺️ **خرائط مجانية عالية الجودة** باستخدام OpenStreetMap
- 📍 **تحديد موقع دقيق** مع GPS متقدم
- 🚗 **أنواع رحلات متعددة** (فردية، جماعية، فاخرة، اقتصادية)
- 💰 **حساب دقيق للسعر** والمسافة والوقت المتوقع
- 📊 **تاريخ شامل للرحلات** مع التقييمات
- 🔄 **تتبع مباشر للرحلة** مع معلومات السائق

### 👤 **الملف الشخصي المحسن**
- 🔔 **نظام إشعارات متقدم** مع تصنيف ذكي
- ⚙️ **إعدادات شاملة** للتطبيق والحساب
- 📊 **إحصائيات تفاعلية** للاستخدام
- 🎨 **تخصيص الواجهة** مع ثيمات متعددة

---

## 🎨 التحسينات الجديدة

### **واجهة مستخدم متطورة**
- ✨ **رسوم متحركة سلسة** لجميع التفاعلات
- 🎯 **تصميم متجاوب** يتكيف مع جميع أحجام الشاشات
- 🌈 **ألوان ديناميكية** تتغير حسب السياق
- 📱 **تنقل محسن** مع تأثيرات بصرية جذابة

### **مكونات ذكية جديدة**
- 🔄 **مؤشرات تحميل متحركة** ومخصصة
- 💬 **رسائل تفاعلية** للنجاح والخطأ والتحذير
- ⭐ **نظام تقييم متقدم** بالنجوم مع تأثيرات
- 🖼️ **معرض صور محسن** مع عرض ملء الشاشة
- 🔍 **فلاتر متقدمة** للبحث والتصنيف

### **خرائط محسنة**
- 🆓 **خرائط مجانية** عالية الجودة
- 🎮 **عناصر تحكم مخصصة** للتكبير والتصغير
- 📍 **مؤشرات موقع ذكية** مع معلومات تفاعلية
- 🔍 **بحث الموقع المتقدم** مع اقتراحات

---

## 🛠️ التقنيات المستخدمة

### **الإطار الأساسي**
- **Flutter 3.10+** - أحدث إصدار مع مميزات متقدمة
- **Dart 3.0+** - لغة برمجة حديثة وقوية
- **Material Design 3** - تصميم عصري ومتسق

### **إدارة الحالة والبيانات**
- **Provider** - إدارة حالة متقدمة وفعالة
- **SharedPreferences** - تخزين محلي للإعدادات
- **HTTP** - التواصل مع الخوادم

### **الخرائط والموقع**
- **flutter_map** - خرائط مجانية عالية الأداء
- **latlong2** - حسابات المواقع الجغرافية الدقيقة
- **OpenStreetMap** - بيانات خرائط مجانية ومفتوحة المصدر

### **الواجهة والتفاعل**
- **Animations** - رسوم متحركة سلسة
- **Custom Widgets** - مكونات مخصصة ومتطورة
- **Responsive Design** - تصميم متجاوب

---

## 📋 متطلبات التشغيل

### **متطلبات التطوير**
- Flutter SDK 3.10.0 أو أحدث
- Dart 3.0.0 أو أحدث
- Android Studio أو VS Code
- Git للتحكم في الإصدارات

### **متطلبات الجهاز**
- **Android**: API level 21+ (Android 5.0+)
- **iOS**: iOS 11.0+
- **Web**: متصفحات حديثة
- **RAM**: 2GB كحد أدنى، 4GB مُوصى به

---

## 🚀 التثبيت والتشغيل

### **1. استنساخ المشروع**
```bash
git clone https://github.com/your-username/multi_service_app.git
cd multi_service_app
```

### **2. تثبيت المكتبات**
```bash
flutter pub get
```

### **3. التحقق من البيئة**
```bash
flutter doctor
```

### **4. تشغيل التطبيق**

#### **على الموبايل:**
```bash
flutter run
```

#### **على الويب:**
```bash
flutter run -d web-server --web-port 8080
```

#### **بناء للإنتاج:**
```bash
# Android
flutter build apk --release

# iOS
flutter build ios --release

# Web
flutter build web --release
```

---

## 📁 بنية المشروع المحسنة

```
lib/
├── 📄 main.dart                    # نقطة البداية الرئيسية
├── 📁 models/                      # نماذج البيانات
│   ├── 👤 user.dart               # نموذج المستخدم
│   ├── 🏨 hotel.dart              # نموذج الفندق
│   ├── 🏠 real_estate.dart        # نموذج العقار
│   └── 🚕 taxi.dart               # نموذج التكسي
├── 📁 providers/                   # إدارة الحالة
│   ├── 👤 user_provider.dart      # إدارة بيانات المستخدم
│   ├── 🏨 hotels_provider.dart    # إدارة بيانات الفنادق
│   ├── 🏠 real_estate_provider.dart # إدارة بيانات العقارات
│   └── 🚕 taxi_provider.dart      # إدارة بيانات التكسي
├── 📁 screens/                     # شاشات التطبيق
│   ├── 📄 main_screen.dart        # الشاشة الرئيسية المحسنة
│   ├── 📁 hotels/                 # شاشات الفنادق
│   ├── 📁 real_estate/            # شاشات العقارات
│   ├── 📁 taxi/                   # شاشات التكسي
│   └── 📁 profile/                # شاشات الملف الشخصي
├── 📁 widgets/                     # المكونات المشتركة المحسنة
│   ├── 🔄 loading_widget.dart     # مؤشرات التحميل
│   ├── 💬 message_widgets.dart    # رسائل تفاعلية
│   ├── ⭐ rating_widget.dart      # نظام التقييم
│   ├── 🖼️ image_widgets.dart      # معرض الصور
│   ├── 🔍 filter_widgets.dart     # فلاتر البحث
│   ├── 🗺️ map_widgets.dart        # مكونات الخرائط
│   ├── ✨ enhanced_widgets.dart   # مكونات محسنة
│   └── 📊 stats_widgets.dart      # ويدجات الإحصائيات
├── 📁 utils/                       # المساعدات والثوابت
│   ├── 🎨 constants.dart          # الثوابت والألوان
│   ├── 🛠️ helpers.dart            # دوال مساعدة
│   └── 🌐 api_service.dart        # خدمات API
└── 📁 web/                         # ملفات الويب المحسنة
    ├── 📄 index.html              # صفحة الويب الرئيسية
    ├── 📄 manifest.json           # إعدادات PWA
    └── 📁 icons/                  # أيقونات التطبيق
```

---

## 🎯 كيفية الاستخدام

### **البدء السريع**
1. **افتح التطبيق** وستظهر الشاشة الرئيسية مع الأقسام الثلاثة
2. **اختر القسم المطلوب** من شريط التنقل السفلي
3. **استخدم البحث والفلاتر** للعثور على ما تريد
4. **تفاعل مع المحتوى** باستخدام الإعجاب والتقييم

### **نصائح للاستخدام الأمثل**
- 📱 **استخدم الفلاتر** لتضييق نتائج البحث
- ❤️ **احفظ المفضلة** للوصول السريع لاحقاً
- 🔔 **فعّل الإشعارات** لتلقي التحديثات المهمة
- ⚙️ **خصص الإعدادات** حسب تفضيلاتك

---

## 🤝 المساهمة

نرحب بمساهماتكم لتطوير التطبيق! 

### **كيفية المساهمة**
1. **Fork** المشروع
2. **إنشاء فرع جديد** للميزة: `git checkout -b feature/amazing-feature`
3. **Commit** التغييرات: `git commit -m 'Add amazing feature'`
4. **Push** للفرع: `git push origin feature/amazing-feature`
5. **فتح Pull Request**

### **إرشادات المساهمة**
- 📝 **اتبع معايير الكود** المستخدمة في المشروع
- ✅ **أضف اختبارات** للمميزات الجديدة
- 📚 **حدث الوثائق** عند الحاجة
- 🐛 **أبلغ عن الأخطاء** بتفاصيل واضحة

---

## 📄 الترخيص

هذا المشروع مرخص تحت **رخصة MIT** - انظر ملف [LICENSE](LICENSE) للتفاصيل الكاملة.

---

<div align="center">

**صُنع بـ ❤️ في المملكة العربية السعودية**

[⬆️ العودة للأعلى](#-تطبيق-الخدمات-المتعددة---الإصدار-المحسن)

</div>
