import 'package:flutter/material.dart';
import '../models/hotel.dart';

class HotelsProvider with ChangeNotifier {
  List<Hotel> _hotels = [];
  List<Hotel> _filteredHotels = [];
  List<Hotel> _favoriteHotels = [];
  List<HotelBooking> _bookings = [];
  bool _isLoading = false;
  String? _error;

  List<Hotel> get hotels => _hotels;
  List<Hotel> get filteredHotels => _filteredHotels;
  List<Hotel> get favoriteHotels => _favoriteHotels;
  List<HotelBooking> get bookings => _bookings;
  bool get isLoading => _isLoading;
  String? get error => _error;

  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // تحميل الفنادق
  Future<void> loadHotels() async {
    try {
      setLoading(true);
      setError(null);

      // محاكاة API call
      await Future.delayed(const Duration(seconds: 2));

      // بيانات تجريبية
      _hotels = [
        Hotel(
          id: '1',
          name: 'فندق الريتز كارلتون',
          description: 'فندق فاخر في قلب المدينة مع إطلالة رائعة',
          address: 'الرياض، المملكة العربية السعودية',
          latitude: 24.7136,
          longitude: 46.6753,
          images: [
            'https://example.com/hotel1_1.jpg',
            'https://example.com/hotel1_2.jpg',
          ],
          stars: 5,
          rating: 4.8,
          reviewsCount: 245,
          type: 'فندق',
          rooms: [
            Room(
              id: '1',
              type: 'غرفة مفردة',
              price: 450.0,
              maxGuests: 1,
              amenities: ['واي فاي', 'تكييف', 'تلفزيون'],
            ),
            Room(
              id: '2',
              type: 'غرفة مزدوجة',
              price: 650.0,
              maxGuests: 2,
              amenities: ['واي فاي', 'تكييف', 'تلفزيون', 'ميني بار'],
            ),
          ],
          amenities: ['مسبح', 'جيم', 'سبا', 'مطعم', 'موقف سيارات'],
          isFeatured: true,
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
        ),
        Hotel(
          id: '2',
          name: 'شقق الواحة المفروشة',
          description: 'شقق مفروشة بالكامل مع مطبخ وغرفة معيشة',
          address: 'جدة، المملكة العربية السعودية',
          latitude: 21.3891,
          longitude: 39.8579,
          images: [
            'https://example.com/hotel2_1.jpg',
            'https://example.com/hotel2_2.jpg',
          ],
          stars: 4,
          rating: 4.5,
          reviewsCount: 128,
          type: 'شقق',
          rooms: [
            Room(
              id: '3',
              type: 'شقة بغرفة واحدة',
              price: 280.0,
              maxGuests: 2,
              amenities: ['واي فاي', 'مطبخ', 'غسالة', 'تكييف'],
            ),
            Room(
              id: '4',
              type: 'شقة بغرفتين',
              price: 420.0,
              maxGuests: 4,
              amenities: ['واي فاي', 'مطبخ', 'غسالة', 'تكييف', 'شرفة'],
            ),
          ],
          amenities: ['واي فاي', 'موقف سيارات', 'أمن 24 ساعة'],
          createdAt: DateTime.now().subtract(const Duration(days: 15)),
        ),
      ];

      _filteredHotels = List.from(_hotels);
      setLoading(false);
    } catch (e) {
      setError('خطأ في تحميل الفنادق: ${e.toString()}');
      setLoading(false);
    }
  }

  // البحث والفلترة
  void searchAndFilter({
    String? searchQuery,
    int? minStars,
    int? maxStars,
    double? minPrice,
    double? maxPrice,
    String? hotelType,
    int? minGuests,
  }) {
    _filteredHotels = _hotels.where((hotel) {
      bool matches = true;

      if (searchQuery != null && searchQuery.isNotEmpty) {
        matches = matches && 
            (hotel.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
             hotel.address.toLowerCase().contains(searchQuery.toLowerCase()));
      }

      if (minStars != null) {
        matches = matches && hotel.stars >= minStars;
      }

      if (maxStars != null) {
        matches = matches && hotel.stars <= maxStars;
      }

      if (hotelType != null && hotelType.isNotEmpty) {
        matches = matches && hotel.type == hotelType;
      }

      if (minPrice != null || maxPrice != null || minGuests != null) {
        bool roomMatches = hotel.rooms.any((room) {
          bool roomMatch = true;
          
          if (minPrice != null) {
            roomMatch = roomMatch && room.price >= minPrice;
          }
          
          if (maxPrice != null) {
            roomMatch = roomMatch && room.price <= maxPrice;
          }
          
          if (minGuests != null) {
            roomMatch = roomMatch && room.maxGuests >= minGuests;
          }
          
          return roomMatch;
        });
        matches = matches && roomMatches;
      }

      return matches;
    }).toList();

    notifyListeners();
  }

  // إضافة/إزالة من المفضلة
  void toggleFavorite(String hotelId) {
    final hotel = _hotels.firstWhere((h) => h.id == hotelId);
    
    if (_favoriteHotels.any((h) => h.id == hotelId)) {
      _favoriteHotels.removeWhere((h) => h.id == hotelId);
    } else {
      _favoriteHotels.add(hotel);
    }
    
    notifyListeners();
  }

  bool isFavorite(String hotelId) {
    return _favoriteHotels.any((h) => h.id == hotelId);
  }

  // حجز فندق
  Future<bool> bookHotel({
    required String hotelId,
    required String roomId,
    required String userId,
    required DateTime checkIn,
    required DateTime checkOut,
    required int guests,
  }) async {
    try {
      setLoading(true);
      setError(null);

      // حساب السعر الإجمالي
      final hotel = _hotels.firstWhere((h) => h.id == hotelId);
      final room = hotel.rooms.firstWhere((r) => r.id == roomId);
      final nights = checkOut.difference(checkIn).inDays;
      final totalPrice = room.price * nights;

      // محاكاة API call
      await Future.delayed(const Duration(seconds: 2));

      final booking = HotelBooking(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        hotelId: hotelId,
        roomId: roomId,
        userId: userId,
        checkIn: checkIn,
        checkOut: checkOut,
        guests: guests,
        totalPrice: totalPrice,
        status: 'confirmed',
        createdAt: DateTime.now(),
      );

      _bookings.add(booking);
      setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      setError('خطأ في الحجز: ${e.toString()}');
      setLoading(false);
      return false;
    }
  }

  // تحميل الحجوزات
  Future<void> loadBookings(String userId) async {
    try {
      setLoading(true);
      setError(null);

      // محاكاة API call
      await Future.delayed(const Duration(seconds: 1));

      // فلترة الحجوزات حسب المستخدم
      _bookings = _bookings.where((b) => b.userId == userId).toList();

      setLoading(false);
    } catch (e) {
      setError('خطأ في تحميل الحجوزات: ${e.toString()}');
      setLoading(false);
    }
  }
}
