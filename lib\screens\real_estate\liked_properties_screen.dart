import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/real_estate_provider.dart';
import '../../providers/user_provider.dart';
import '../../widgets/real_estate_card.dart';

class LikedPropertiesScreen extends StatefulWidget {
  const LikedPropertiesScreen({super.key});

  @override
  State<LikedPropertiesScreen> createState() => _LikedPropertiesScreenState();
}

class _LikedPropertiesScreenState extends State<LikedPropertiesScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final userProvider = context.read<UserProvider>();
      if (userProvider.isLoggedIn) {
        context.read<RealEstateProvider>().loadLikedProperties(userProvider.currentUser!.id);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('العقارات المفضلة'),
      ),
      body: Consumer2<RealEstateProvider, UserProvider>(
        builder: (context, realEstateProvider, userProvider, child) {
          if (!userProvider.isLoggedIn) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.login,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'يرجى تسجيل الدخول لعرض العقارات المفضلة',
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          if (realEstateProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          final likedProperties = realEstateProvider.likedProperties;

          if (likedProperties.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.favorite_border,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد عقارات مفضلة',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'اضغط على ❤️ في أي عقار لإضافته للمفضلة',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.search),
                    label: const Text('تصفح العقارات'),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              realEstateProvider.loadLikedProperties(userProvider.currentUser!.id);
            },
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: likedProperties.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: RealEstateCard(property: likedProperties[index]),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
