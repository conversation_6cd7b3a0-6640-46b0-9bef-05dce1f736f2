import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/user_provider.dart';
import '../../widgets/message_widgets.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _locationEnabled = true;
  bool _darkModeEnabled = false;
  String _selectedLanguage = 'ar';
  String _selectedCurrency = 'SAR';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
      ),
      body: ListView(
        children: [
          // قسم الحساب
          _buildSectionHeader('الحساب'),
          _buildAccountSettings(),
          
          const SizedBox(height: 16),
          
          // قسم التطبيق
          _buildSectionHeader('التطبيق'),
          _buildAppSettings(),
          
          const SizedBox(height: 16),
          
          // قسم الخصوصية والأمان
          _buildSectionHeader('الخصوصية والأمان'),
          _buildPrivacySettings(),
          
          const SizedBox(height: 16),
          
          // قسم الدعم
          _buildSectionHeader('الدعم والمساعدة'),
          _buildSupportSettings(),
          
          const SizedBox(height: 16),
          
          // قسم حول التطبيق
          _buildSectionHeader('حول التطبيق'),
          _buildAboutSettings(),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.blue[700],
        ),
      ),
    );
  }

  Widget _buildAccountSettings() {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        return Column(
          children: [
            _buildSettingsTile(
              icon: Icons.person,
              title: 'تحرير الملف الشخصي',
              subtitle: 'تعديل المعلومات الشخصية',
              onTap: () {
                Navigator.pushNamed(context, '/edit-profile');
              },
            ),
            _buildSettingsTile(
              icon: Icons.lock,
              title: 'تغيير كلمة المرور',
              subtitle: 'تحديث كلمة المرور',
              onTap: () {
                _showChangePasswordDialog();
              },
            ),
            _buildSettingsTile(
              icon: Icons.email,
              title: 'تغيير البريد الإلكتروني',
              subtitle: userProvider.currentUser?.email ?? '',
              onTap: () {
                _showChangeEmailDialog();
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildAppSettings() {
    return Column(
      children: [
        _buildSwitchTile(
          icon: Icons.notifications,
          title: 'الإشعارات',
          subtitle: 'تلقي إشعارات التطبيق',
          value: _notificationsEnabled,
          onChanged: (value) {
            setState(() {
              _notificationsEnabled = value;
            });
          },
        ),
        _buildSwitchTile(
          icon: Icons.location_on,
          title: 'خدمات الموقع',
          subtitle: 'السماح بالوصول للموقع',
          value: _locationEnabled,
          onChanged: (value) {
            setState(() {
              _locationEnabled = value;
            });
          },
        ),
        _buildSwitchTile(
          icon: Icons.dark_mode,
          title: 'الوضع الليلي',
          subtitle: 'تفعيل الثيم الداكن',
          value: _darkModeEnabled,
          onChanged: (value) {
            setState(() {
              _darkModeEnabled = value;
            });
          },
        ),
        _buildSettingsTile(
          icon: Icons.language,
          title: 'اللغة',
          subtitle: _selectedLanguage == 'ar' ? 'العربية' : 'English',
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: () {
            _showLanguageDialog();
          },
        ),
        _buildSettingsTile(
          icon: Icons.attach_money,
          title: 'العملة',
          subtitle: _selectedCurrency == 'SAR' ? 'ريال سعودي' : 'دولار أمريكي',
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: () {
            _showCurrencyDialog();
          },
        ),
      ],
    );
  }

  Widget _buildPrivacySettings() {
    return Column(
      children: [
        _buildSettingsTile(
          icon: Icons.privacy_tip,
          title: 'سياسة الخصوصية',
          subtitle: 'اطلع على سياسة الخصوصية',
          onTap: () {
            _showPrivacyPolicy();
          },
        ),
        _buildSettingsTile(
          icon: Icons.description,
          title: 'شروط الاستخدام',
          subtitle: 'اقرأ شروط وأحكام الاستخدام',
          onTap: () {
            _showTermsOfService();
          },
        ),
        _buildSettingsTile(
          icon: Icons.security,
          title: 'الأمان',
          subtitle: 'إعدادات الأمان والحماية',
          onTap: () {
            _showSecuritySettings();
          },
        ),
        _buildSettingsTile(
          icon: Icons.delete_forever,
          title: 'حذف الحساب',
          subtitle: 'حذف الحساب نهائياً',
          titleColor: Colors.red,
          onTap: () {
            _showDeleteAccountDialog();
          },
        ),
      ],
    );
  }

  Widget _buildSupportSettings() {
    return Column(
      children: [
        _buildSettingsTile(
          icon: Icons.help,
          title: 'مركز المساعدة',
          subtitle: 'الأسئلة الشائعة والدعم',
          onTap: () {
            _showHelpCenter();
          },
        ),
        _buildSettingsTile(
          icon: Icons.feedback,
          title: 'إرسال ملاحظات',
          subtitle: 'شاركنا رأيك واقتراحاتك',
          onTap: () {
            _showFeedbackDialog();
          },
        ),
        _buildSettingsTile(
          icon: Icons.star_rate,
          title: 'تقييم التطبيق',
          subtitle: 'قيم التطبيق في المتجر',
          onTap: () {
            _rateApp();
          },
        ),
        _buildSettingsTile(
          icon: Icons.phone,
          title: 'اتصل بنا',
          subtitle: '+966-50-123-4567',
          onTap: () {
            _contactSupport();
          },
        ),
      ],
    );
  }

  Widget _buildAboutSettings() {
    return Column(
      children: [
        _buildSettingsTile(
          icon: Icons.info,
          title: 'حول التطبيق',
          subtitle: 'الإصدار 1.0.0',
          onTap: () {
            _showAboutDialog();
          },
        ),
        _buildSettingsTile(
          icon: Icons.update,
          title: 'التحديثات',
          subtitle: 'البحث عن تحديثات جديدة',
          onTap: () {
            _checkForUpdates();
          },
        ),
        _buildSettingsTile(
          icon: Icons.share,
          title: 'مشاركة التطبيق',
          subtitle: 'شارك التطبيق مع الأصدقاء',
          onTap: () {
            _shareApp();
          },
        ),
      ],
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    String? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
    Color? titleColor,
  }) {
    return ListTile(
      leading: Icon(icon, color: titleColor),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w500,
          color: titleColor,
        ),
      ),
      subtitle: subtitle != null ? Text(subtitle) : null,
      trailing: trailing ?? const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  Widget _buildSwitchTile({
    required IconData icon,
    required String title,
    String? subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w500),
      ),
      subtitle: subtitle != null ? Text(subtitle) : null,
      trailing: Switch(
        value: value,
        onChanged: onChanged,
      ),
    );
  }

  void _showChangePasswordDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير كلمة المرور'),
        content: const Text('ميزة تغيير كلمة المرور قيد التطوير'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showChangeEmailDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير البريد الإلكتروني'),
        content: const Text('ميزة تغيير البريد الإلكتروني قيد التطوير'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار اللغة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('العربية'),
              value: 'ar',
              groupValue: _selectedLanguage,
              onChanged: (value) {
                setState(() {
                  _selectedLanguage = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('English'),
              value: 'en',
              groupValue: _selectedLanguage,
              onChanged: (value) {
                setState(() {
                  _selectedLanguage = value!;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCurrencyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار العملة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('ريال سعودي (SAR)'),
              value: 'SAR',
              groupValue: _selectedCurrency,
              onChanged: (value) {
                setState(() {
                  _selectedCurrency = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('دولار أمريكي (USD)'),
              value: 'USD',
              groupValue: _selectedCurrency,
              onChanged: (value) {
                setState(() {
                  _selectedCurrency = value!;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showPrivacyPolicy() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const WebViewScreen(
          title: 'سياسة الخصوصية',
          url: 'https://example.com/privacy',
        ),
      ),
    );
  }

  void _showTermsOfService() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const WebViewScreen(
          title: 'شروط الاستخدام',
          url: 'https://example.com/terms',
        ),
      ),
    );
  }

  void _showSecuritySettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعدادات الأمان قيد التطوير')),
    );
  }

  void _showDeleteAccountDialog() {
    ConfirmationDialog.show(
      context,
      title: 'حذف الحساب',
      message: 'هل أنت متأكد من رغبتك في حذف حسابك نهائياً؟ لا يمكن التراجع عن هذا الإجراء.',
      confirmText: 'حذف',
      confirmColor: Colors.red,
      icon: Icons.warning,
      onConfirm: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('ميزة حذف الحساب قيد التطوير')),
        );
      },
    );
  }

  void _showHelpCenter() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مركز المساعدة قيد التطوير')),
    );
  }

  void _showFeedbackDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('نموذج الملاحظات قيد التطوير')),
    );
  }

  void _rateApp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم فتح متجر التطبيقات للتقييم')),
    );
  }

  void _contactSupport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم فتح تطبيق الهاتف للاتصال')),
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'تطبيق الخدمات المتعددة',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(Icons.apps, size: 64),
      children: [
        const Text('تطبيق شامل للفنادق والعقارات والتكسي'),
        const SizedBox(height: 16),
        const Text('تم تطوير هذا التطبيق باستخدام Flutter'),
      ],
    );
  }

  void _checkForUpdates() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('لا توجد تحديثات متاحة حالياً')),
    );
  }

  void _shareApp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة التطبيق قيد التطوير')),
    );
  }
}

class WebViewScreen extends StatelessWidget {
  final String title;
  final String url;

  const WebViewScreen({
    super.key,
    required this.title,
    required this.url,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.web, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'عارض الويب قيد التطوير',
              style: TextStyle(fontSize: 18),
            ),
            SizedBox(height: 8),
            Text(
              'سيتم عرض المحتوى هنا',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
