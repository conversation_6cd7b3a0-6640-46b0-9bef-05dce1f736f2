#!/bin/bash

# تعيين الترميز للعربية
export LANG=ar_SA.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# دالة لطباعة النص الملون
print_colored() {
    echo -e "${2}${1}${NC}"
}

# دالة لطباعة العنوان
print_header() {
    echo
    print_colored "========================================" $CYAN
    print_colored "    🚀 تطبيق الخدمات المتعددة" $CYAN
    print_colored "========================================" $CYAN
    echo
}

# دالة للتحقق من وجود Flutter
check_flutter() {
    if ! command -v flutter &> /dev/null; then
        print_colored "❌ Flutter غير مثبت! يرجى تثبيت Flutter أولاً." $RED
        print_colored "🔗 https://flutter.dev/docs/get-started/install" $BLUE
        exit 1
    fi
}

# دالة لتشغيل الأوامر مع عرض الحالة
run_command() {
    local cmd="$1"
    local desc="$2"
    
    print_colored "$desc..." $YELLOW
    if eval "$cmd"; then
        print_colored "✅ تم بنجاح!" $GREEN
    else
        print_colored "❌ فشل في التنفيذ!" $RED
        exit 1
    fi
    echo
}

# دالة لعرض القائمة
show_menu() {
    print_colored "اختر منصة التشغيل:" $PURPLE
    echo "1. 📱 Android (الموبايل)"
    echo "2. 🌐 Web (المتصفح)"
    echo "3. 🍎 iOS (آيفون/آيباد)"
    echo "4. 💻 macOS (ماك)"
    echo "5. 🐧 Linux (لينكس)"
    echo "6. 📋 إظهار الأجهزة المتاحة"
    echo "7. 🔧 تنظيف وإعادة بناء المشروع"
    echo "8. 🚪 خروج"
    echo
}

# دالة لتشغيل التطبيق على منصة معينة
run_on_platform() {
    local platform="$1"
    local platform_name="$2"
    
    print_colored "تشغيل التطبيق على $platform_name..." $BLUE
    
    case $platform in
        "android")
            flutter run -d android
            ;;
        "web")
            print_colored "🔗 سيتم فتح التطبيق على: http://localhost:8080" $CYAN
            flutter run -d web-server --web-port 8080 --web-hostname 0.0.0.0
            ;;
        "ios")
            flutter run -d ios
            ;;
        "macos")
            flutter run -d macos
            ;;
        "linux")
            flutter run -d linux
            ;;
    esac
}

# دالة لإظهار الأجهزة المتاحة
show_devices() {
    print_colored "📱 الأجهزة المتاحة:" $PURPLE
    flutter devices
    echo
    read -p "اضغط Enter للمتابعة..."
}

# دالة لتنظيف المشروع
clean_project() {
    print_colored "🧹 تنظيف المشروع..." $YELLOW
    
    run_command "flutter clean" "تنظيف ملفات البناء"
    run_command "flutter pub get" "تثبيت المكتبات"
    
    # تشغيل build_runner إذا كان موجوداً
    if grep -q "build_runner" pubspec.yaml; then
        run_command "flutter packages pub run build_runner build --delete-conflicting-outputs" "إنشاء ملفات الكود المطلوبة"
    fi
    
    print_colored "✅ تم تنظيف المشروع بنجاح!" $GREEN
    read -p "اضغط Enter للمتابعة..."
}

# البرنامج الرئيسي
main() {
    print_header
    
    # التحقق من وجود Flutter
    check_flutter
    
    # التحقق من حالة Flutter
    print_colored "📋 التحقق من متطلبات Flutter..." $YELLOW
    flutter doctor
    echo
    
    # تثبيت المكتبات
    run_command "flutter pub get" "📦 تثبيت المكتبات المطلوبة"
    
    # حلقة القائمة الرئيسية
    while true; do
        show_menu
        read -p "أدخل اختيارك (1-8): " choice
        
        case $choice in
            1)
                run_on_platform "android" "Android"
                ;;
            2)
                run_on_platform "web" "Web"
                ;;
            3)
                run_on_platform "ios" "iOS"
                ;;
            4)
                run_on_platform "macos" "macOS"
                ;;
            5)
                run_on_platform "linux" "Linux"
                ;;
            6)
                show_devices
                ;;
            7)
                clean_project
                ;;
            8)
                print_colored "👋 شكراً لاستخدام تطبيق الخدمات المتعددة!" $GREEN
                exit 0
                ;;
            *)
                print_colored "❌ اختيار غير صحيح! يرجى اختيار رقم من 1 إلى 8." $RED
                ;;
        esac
        
        echo
        print_colored "✅ تم إنهاء التطبيق" $GREEN
        read -p "اضغط Enter للعودة للقائمة الرئيسية..."
        clear
        print_header
    done
}

# تشغيل البرنامج الرئيسي
main "$@"
