import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/user_provider.dart';
import 'login_screen.dart';
import 'edit_profile_screen.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        if (!userProvider.isLoggedIn) {
          return const LoginScreen();
        }

        final user = userProvider.currentUser!;

        return Scaffold(
          appBar: AppBar(
            title: const Text('الملف الشخصي'),
            actions: [
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const EditProfileScreen(),
                    ),
                  );
                },
              ),
            ],
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // صورة الملف الشخصي والمعلومات الأساسية
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // صورة الملف الشخصي
                      CircleAvatar(
                        radius: 50,
                        backgroundColor: Colors.blue[100],
                        backgroundImage: user.profileImage != null
                            ? NetworkImage(user.profileImage!)
                            : null,
                        child: user.profileImage == null
                            ? Icon(
                                Icons.person,
                                size: 50,
                                color: Colors.blue[700],
                              )
                            : null,
                      ),
                      const SizedBox(height: 16),
                      
                      // الاسم
                      Text(
                        user.name,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      
                      // البريد الإلكتروني
                      Text(
                        user.email,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 4),
                      
                      // رقم الهاتف
                      Text(
                        user.phone,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      // نوع المستخدم
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(color: Colors.blue[200]!),
                        ),
                        child: Text(
                          _getUserTypeText(user.userType),
                          style: TextStyle(
                            color: Colors.blue[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // قائمة الخيارات
                _buildMenuSection(context, [
                  _buildMenuItem(
                    icon: Icons.hotel,
                    title: 'حجوزات الفنادق',
                    subtitle: 'عرض وإدارة حجوزاتك',
                    onTap: () {
                      // التنقل لشاشة حجوزات الفنادق
                    },
                  ),
                  _buildMenuItem(
                    icon: Icons.favorite,
                    title: 'العقارات المفضلة',
                    subtitle: 'العقارات التي أعجبت بها',
                    onTap: () {
                      // التنقل لشاشة العقارات المفضلة
                    },
                  ),
                  _buildMenuItem(
                    icon: Icons.history,
                    title: 'تاريخ الرحلات',
                    subtitle: 'رحلات التكسي السابقة',
                    onTap: () {
                      // التنقل لشاشة تاريخ الرحلات
                    },
                  ),
                ]),

                const SizedBox(height: 16),

                _buildMenuSection(context, [
                  _buildMenuItem(
                    icon: Icons.notifications,
                    title: 'الإشعارات',
                    subtitle: 'إدارة الإشعارات',
                    onTap: () {
                      // التنقل لشاشة الإشعارات
                    },
                  ),
                  _buildMenuItem(
                    icon: Icons.language,
                    title: 'اللغة',
                    subtitle: 'العربية',
                    onTap: () {
                      // تغيير اللغة
                    },
                  ),
                  _buildMenuItem(
                    icon: Icons.help,
                    title: 'المساعدة والدعم',
                    subtitle: 'الأسئلة الشائعة والدعم',
                    onTap: () {
                      // التنقل لشاشة المساعدة
                    },
                  ),
                  _buildMenuItem(
                    icon: Icons.info,
                    title: 'حول التطبيق',
                    subtitle: 'معلومات التطبيق والإصدار',
                    onTap: () {
                      // عرض معلومات التطبيق
                    },
                  ),
                ]),

                const SizedBox(height: 24),

                // زر تسجيل الخروج
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      _showLogoutDialog(context, userProvider);
                    },
                    icon: const Icon(Icons.logout),
                    label: const Text('تسجيل الخروج'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),

                const SizedBox(height: 32),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMenuSection(BuildContext context, List<Widget> items) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: items,
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.blue[50],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: Colors.blue[700],
          size: 24,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 12,
        ),
      ),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  String _getUserTypeText(String userType) {
    switch (userType) {
      case 'passenger':
        return 'راكب';
      case 'driver':
        return 'سائق';
      case 'hotel_owner':
        return 'مالك فندق';
      case 'admin':
        return 'مدير';
      default:
        return 'مستخدم';
    }
  }

  void _showLogoutDialog(BuildContext context, UserProvider userProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              userProvider.logout();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}
