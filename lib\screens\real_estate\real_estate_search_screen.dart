import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/real_estate_provider.dart';
import '../../models/real_estate.dart';
import '../../widgets/real_estate_card.dart';
import '../../widgets/search_bar_widget.dart';

class RealEstateSearchScreen extends StatefulWidget {
  const RealEstateSearchScreen({super.key});

  @override
  State<RealEstateSearchScreen> createState() => _RealEstateSearchScreenState();
}

class _RealEstateSearchScreenState extends State<RealEstateSearchScreen> {
  final _searchController = TextEditingController();
  
  // فلاتر البحث
  String? _selectedListingType;
  String? _selectedCategory;
  String? _selectedSubCategory;
  double? _selectedMinPrice;
  double? _selectedMaxPrice;
  double? _selectedMinArea;
  double? _selectedMaxArea;
  int? _selectedMinBedrooms;
  int? _selectedMaxBedrooms;
  String? _selectedCity;

  final List<String> _listingTypes = ['شراء', 'إيجار'];
  final List<String> _categories = ['سكني', 'تجاري', 'زراعي'];
  final List<String> _subCategories = ['طابو', 'زراعي'];
  final List<String> _cities = ['الرياض', 'جدة', 'الدمام', 'مكة', 'المدينة', 'الطائف', 'بريدة', 'تبوك'];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _applyFilters() {
    final filter = RealEstateFilter(
      listingType: _selectedListingType,
      category: _selectedCategory,
      subCategory: _selectedSubCategory,
      minPrice: _selectedMinPrice,
      maxPrice: _selectedMaxPrice,
      minArea: _selectedMinArea,
      maxArea: _selectedMaxArea,
      minBedrooms: _selectedMinBedrooms,
      maxBedrooms: _selectedMaxBedrooms,
      city: _selectedCity,
    );

    context.read<RealEstateProvider>().searchAndFilter(
      filter,
      searchQuery: _searchController.text,
    );
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _selectedListingType = null;
      _selectedCategory = null;
      _selectedSubCategory = null;
      _selectedMinPrice = null;
      _selectedMaxPrice = null;
      _selectedMinArea = null;
      _selectedMaxArea = null;
      _selectedMinBedrooms = null;
      _selectedMaxBedrooms = null;
      _selectedCity = null;
    });
    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('البحث في العقارات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: _clearFilters,
            tooltip: 'مسح الفلاتر',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلاتر
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[50],
            child: Column(
              children: [
                // شريط البحث
                SearchBarWidget(
                  controller: _searchController,
                  hintText: 'ابحث عن عقار...',
                  onChanged: (query) {
                    _applyFilters();
                  },
                ),

                const SizedBox(height: 16),

                // فلاتر سريعة
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip(
                        'نوع الإعلان',
                        _selectedListingType != null,
                        () => _showListingTypeFilter(),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'الفئة',
                        _selectedCategory != null,
                        () => _showCategoryFilter(),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'السعر',
                        _selectedMinPrice != null || _selectedMaxPrice != null,
                        () => _showPriceFilter(),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'المساحة',
                        _selectedMinArea != null || _selectedMaxArea != null,
                        () => _showAreaFilter(),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'الغرف',
                        _selectedMinBedrooms != null || _selectedMaxBedrooms != null,
                        () => _showBedroomsFilter(),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'المدينة',
                        _selectedCity != null,
                        () => _showCityFilter(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // النتائج
          Expanded(
            child: Consumer<RealEstateProvider>(
              builder: (context, realEstateProvider, child) {
                if (realEstateProvider.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                final properties = realEstateProvider.filteredProperties;

                if (properties.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد نتائج',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'جرب تعديل معايير البحث',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: properties.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: RealEstateCard(property: properties[index]),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isActive, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive ? Colors.blue : Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isActive ? Colors.blue : Colors.grey[300]!,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: TextStyle(
                color: isActive ? Colors.white : Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
            ),
            if (isActive) ...[
              const SizedBox(width: 4),
              Icon(
                Icons.check,
                size: 16,
                color: Colors.white,
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showListingTypeFilter() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نوع الإعلان',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            ..._listingTypes.map((type) => RadioListTile<String>(
              title: Text(type),
              value: type,
              groupValue: _selectedListingType,
              onChanged: (value) {
                setState(() {
                  _selectedListingType = value;
                });
                Navigator.pop(context);
                _applyFilters();
              },
            )),
            
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedListingType = null;
                });
                Navigator.pop(context);
                _applyFilters();
              },
              child: const Text('مسح الاختيار'),
            ),
          ],
        ),
      ),
    );
  }

  void _showCategoryFilter() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'فئة العقار',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            ..._categories.map((category) => RadioListTile<String>(
              title: Text(category),
              value: category,
              groupValue: _selectedCategory,
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value;
                  _selectedSubCategory = null; // إعادة تعيين الفئة الفرعية
                });
                Navigator.pop(context);
                _applyFilters();
              },
            )),
            
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedCategory = null;
                  _selectedSubCategory = null;
                });
                Navigator.pop(context);
                _applyFilters();
              },
              child: const Text('مسح الاختيار'),
            ),
          ],
        ),
      ),
    );
  }

  void _showPriceFilter() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نطاق السعر (ر.س)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      labelText: 'السعر الأدنى',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      _selectedMinPrice = double.tryParse(value);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      labelText: 'السعر الأعلى',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      _selectedMaxPrice = double.tryParse(value);
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  child: const Text('تطبيق'),
                ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedMinPrice = null;
                      _selectedMaxPrice = null;
                    });
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  child: const Text('مسح'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showAreaFilter() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المساحة (م²)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      labelText: 'المساحة الأدنى',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      _selectedMinArea = double.tryParse(value);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      labelText: 'المساحة الأعلى',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      _selectedMaxArea = double.tryParse(value);
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  child: const Text('تطبيق'),
                ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedMinArea = null;
                      _selectedMaxArea = null;
                    });
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  child: const Text('مسح'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showBedroomsFilter() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'عدد الغرف',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: DropdownButton<int>(
                    value: _selectedMinBedrooms,
                    hint: const Text('الحد الأدنى'),
                    isExpanded: true,
                    items: [1, 2, 3, 4, 5, 6, 7, 8].map((bedrooms) {
                      return DropdownMenuItem(
                        value: bedrooms,
                        child: Text('$bedrooms غرف'),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedMinBedrooms = value;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButton<int>(
                    value: _selectedMaxBedrooms,
                    hint: const Text('الحد الأعلى'),
                    isExpanded: true,
                    items: [1, 2, 3, 4, 5, 6, 7, 8].map((bedrooms) {
                      return DropdownMenuItem(
                        value: bedrooms,
                        child: Text('$bedrooms غرف'),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedMaxBedrooms = value;
                      });
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  child: const Text('تطبيق'),
                ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedMinBedrooms = null;
                      _selectedMaxBedrooms = null;
                    });
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  child: const Text('مسح'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showCityFilter() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المدينة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            ..._cities.map((city) => RadioListTile<String>(
              title: Text(city),
              value: city,
              groupValue: _selectedCity,
              onChanged: (value) {
                setState(() {
                  _selectedCity = value;
                });
                Navigator.pop(context);
                _applyFilters();
              },
            )),
            
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedCity = null;
                });
                Navigator.pop(context);
                _applyFilters();
              },
              child: const Text('مسح الاختيار'),
            ),
          ],
        ),
      ),
    );
  }
}
