class AppConstants {
  // معلومات التطبيق
  static const String appName = 'تطبيق الخدمات المتعددة';
  static const String appVersion = '1.0.0';
  
  // URLs للخرائط
  static const String openStreetMapUrl = 'https://tile.openstreetmap.org/{z}/{x}/{y}.png';
  
  // إعدادات الخرائط
  static const double defaultLatitude = 24.7136; // الرياض
  static const double defaultLongitude = 46.6753;
  static const double defaultZoom = 12.0;
  static const double detailZoom = 15.0;
  
  // إعدادات التكسي
  static const double earthRadiusKm = 6371.0;
  static const double averageSpeedKmh = 40.0; // متوسط السرعة في المدينة
  static const double baseTaxiPrice = 10.0; // السعر الأساسي
  static const double pricePerKm = 2.5; // السعر لكل كيلومتر
  
  // مضاعفات أسعار أنواع الرحلات
  static const Map<String, double> rideTypeMultipliers = {
    'فردية': 1.0,
    'جماعية': 0.8,
    'مريحة': 1.5,
    'فاخرة': 2.0,
  };
  
  // أنواع الفنادق
  static const List<String> hotelTypes = [
    'فندق',
    'شقق',
    'منتجع',
    'بيت ضيافة',
  ];
  
  // أنواع العقارات
  static const List<String> listingTypes = ['شراء', 'إيجار'];
  static const List<String> propertyCategories = ['سكني', 'تجاري', 'زراعي'];
  static const List<String> propertySubCategories = ['طابو', 'زراعي'];
  
  // المدن المدعومة
  static const List<String> supportedCities = [
    'الرياض',
    'جدة',
    'الدمام',
    'مكة المكرمة',
    'المدينة المنورة',
    'الطائف',
    'بريدة',
    'تبوك',
    'خميس مشيط',
    'الأحساء',
    'حائل',
    'جازان',
  ];
  
  // أنواع المستخدمين
  static const List<String> userTypes = [
    'passenger',
    'driver',
    'hotel_owner',
    'admin',
  ];
  
  // حالات الحجوزات
  static const List<String> bookingStatuses = [
    'pending',
    'confirmed',
    'cancelled',
    'completed',
  ];
  
  // حالات الرحلات
  static const List<String> rideStatuses = [
    'pending',
    'accepted',
    'in_progress',
    'completed',
    'cancelled',
  ];
  
  // إعدادات الصور
  static const int maxImagesPerProperty = 10;
  static const int maxImageSizeMB = 5;
  static const List<String> allowedImageFormats = ['jpg', 'jpeg', 'png'];
  
  // إعدادات البحث
  static const int searchResultsLimit = 50;
  static const int featuredItemsLimit = 5;
  
  // إعدادات التقييم
  static const double minRating = 1.0;
  static const double maxRating = 5.0;
  static const double defaultRating = 5.0;
  
  // مدد الانتظار (بالثواني)
  static const int rideRequestTimeout = 300; // 5 دقائق
  static const int driverResponseTimeout = 60; // دقيقة واحدة
  
  // رسائل الخطأ
  static const String networkErrorMessage = 'خطأ في الاتصال بالإنترنت';
  static const String locationErrorMessage = 'خطأ في تحديد الموقع';
  static const String permissionDeniedMessage = 'تم رفض الإذن المطلوب';
  static const String unknownErrorMessage = 'حدث خطأ غير متوقع';
  
  // رسائل النجاح
  static const String loginSuccessMessage = 'تم تسجيل الدخول بنجاح';
  static const String registerSuccessMessage = 'تم إنشاء الحساب بنجاح';
  static const String updateSuccessMessage = 'تم التحديث بنجاح';
  static const String deleteSuccessMessage = 'تم الحذف بنجاح';
  
  // مفاتيح التخزين المحلي
  static const String userDataKey = 'user_data';
  static const String settingsKey = 'app_settings';
  static const String favoritesKey = 'favorites';
  static const String searchHistoryKey = 'search_history';
  
  // إعدادات الخط
  static const String defaultFontFamily = 'Cairo';
  
  // الألوان (Hex)
  static const String primaryColorHex = '#1976D2';
  static const String secondaryColorHex = '#FFC107';
  static const String errorColorHex = '#F44336';
  static const String successColorHex = '#4CAF50';
  static const String warningColorHex = '#FF9800';
  
  // أرقام التواصل
  static const String supportPhone = '+966501234567';
  static const String supportEmail = '<EMAIL>';
  static const String emergencyNumber = '911';
  
  // روابط مهمة
  static const String privacyPolicyUrl = 'https://multiserviceapp.com/privacy';
  static const String termsOfServiceUrl = 'https://multiserviceapp.com/terms';
  static const String helpCenterUrl = 'https://multiserviceapp.com/help';
  
  // إعدادات الإشعارات
  static const String notificationChannelId = 'multi_service_notifications';
  static const String notificationChannelName = 'إشعارات التطبيق';
  static const String notificationChannelDescription = 'إشعارات عامة للتطبيق';
  
  // أوقات التحديث (بالدقائق)
  static const int dataRefreshInterval = 5;
  static const int locationUpdateInterval = 1;
  static const int rideTrackingInterval = 10; // بالثواني
  
  // حدود البيانات
  static const int maxSearchHistoryItems = 20;
  static const int maxFavoriteItems = 100;
  static const int maxRecentItems = 10;
  
  // إعدادات الأمان
  static const int maxLoginAttempts = 5;
  static const int passwordMinLength = 6;
  static const int otpLength = 6;
  static const int otpExpiryMinutes = 5;
}

class AppStrings {
  // عناوين الشاشات
  static const String hotelsTitle = 'الفنادق';
  static const String realEstateTitle = 'العقارات';
  static const String taxiTitle = 'التكسي';
  static const String profileTitle = 'الملف الشخصي';
  
  // أزرار عامة
  static const String saveButton = 'حفظ';
  static const String cancelButton = 'إلغاء';
  static const String confirmButton = 'تأكيد';
  static const String deleteButton = 'حذف';
  static const String editButton = 'تحرير';
  static const String searchButton = 'بحث';
  static const String filterButton = 'فلترة';
  static const String clearButton = 'مسح';
  static const String retryButton = 'إعادة المحاولة';
  static const String closeButton = 'إغلاق';
  static const String backButton = 'رجوع';
  static const String nextButton = 'التالي';
  static const String previousButton = 'السابق';
  static const String submitButton = 'إرسال';
  static const String loginButton = 'تسجيل الدخول';
  static const String registerButton = 'إنشاء حساب';
  static const String logoutButton = 'تسجيل الخروج';
  
  // رسائل التحقق
  static const String requiredField = 'هذا الحقل مطلوب';
  static const String invalidEmail = 'البريد الإلكتروني غير صحيح';
  static const String invalidPhone = 'رقم الهاتف غير صحيح';
  static const String passwordTooShort = 'كلمة المرور قصيرة جداً';
  static const String passwordsNotMatch = 'كلمات المرور غير متطابقة';
  static const String invalidPrice = 'السعر غير صحيح';
  static const String invalidArea = 'المساحة غير صحيحة';
  
  // رسائل الحالة
  static const String loading = 'جاري التحميل...';
  static const String noData = 'لا توجد بيانات';
  static const String noResults = 'لا توجد نتائج';
  static const String noInternet = 'لا يوجد اتصال بالإنترنت';
  static const String locationDisabled = 'خدمة الموقع معطلة';
  static const String permissionRequired = 'الإذن مطلوب';
  
  // وحدات القياس
  static const String currency = 'ر.س';
  static const String areaUnit = 'م²';
  static const String distanceUnit = 'كم';
  static const String timeUnit = 'دقيقة';
  static const String nightUnit = 'ليلة';
  static const String monthUnit = 'شهر';
  static const String yearUnit = 'سنة';
}

class AppAssets {
  // مجلد الصور
  static const String imagesPath = 'assets/images/';
  static const String iconsPath = 'assets/icons/';
  static const String fontsPath = 'assets/fonts/';
  
  // الصور الافتراضية
  static const String defaultHotelImage = '${imagesPath}default_hotel.png';
  static const String defaultPropertyImage = '${imagesPath}default_property.png';
  static const String defaultUserImage = '${imagesPath}default_user.png';
  static const String defaultCarImage = '${imagesPath}default_car.png';
  
  // الأيقونات
  static const String appIcon = '${iconsPath}app_icon.png';
  static const String hotelIcon = '${iconsPath}hotel_icon.png';
  static const String propertyIcon = '${iconsPath}property_icon.png';
  static const String taxiIcon = '${iconsPath}taxi_icon.png';
  
  // الخطوط
  static const String cairoRegular = '${fontsPath}Cairo-Regular.ttf';
  static const String cairoBold = '${fontsPath}Cairo-Bold.ttf';
}
