import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import '../utils/constants.dart';

class CustomMapWidget extends StatefulWidget {
  final LatLng center;
  final double zoom;
  final List<Marker> markers;
  final Function(LatLng)? onTap;
  final Function(LatLng)? onLongPress;
  final bool showCurrentLocation;
  final bool showZoomControls;
  final double? height;

  const CustomMapWidget({
    super.key,
    required this.center,
    this.zoom = 12.0,
    this.markers = const [],
    this.onTap,
    this.onLongPress,
    this.showCurrentLocation = true,
    this.showZoomControls = true,
    this.height,
  });

  @override
  State<CustomMapWidget> createState() => _CustomMapWidgetState();
}

class _CustomMapWidgetState extends State<CustomMapWidget> {
  late MapController _mapController;

  @override
  void initState() {
    super.initState();
    _mapController = MapController();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            FlutterMap(
              mapController: _mapController,
              options: MapOptions(
                center: widget.center,
                zoom: widget.zoom,
                onTap: (tapPosition, point) => widget.onTap?.call(point),
                onLongPress: (tapPosition, point) => widget.onLongPress?.call(point),
                interactiveFlags: InteractiveFlag.all,
              ),
              children: [
                TileLayer(
                  urlTemplate: AppConstants.openStreetMapUrl,
                  userAgentPackageName: 'com.example.multi_service_app',
                ),
                if (widget.markers.isNotEmpty)
                  MarkerLayer(markers: widget.markers),
              ],
            ),
            
            // أزرار التحكم في التكبير
            if (widget.showZoomControls)
              Positioned(
                bottom: 16,
                right: 16,
                child: Column(
                  children: [
                    FloatingActionButton.small(
                      heroTag: "zoom_in",
                      onPressed: () {
                        final zoom = _mapController.zoom + 1;
                        _mapController.move(_mapController.center, zoom);
                      },
                      child: const Icon(Icons.add),
                    ),
                    const SizedBox(height: 8),
                    FloatingActionButton.small(
                      heroTag: "zoom_out",
                      onPressed: () {
                        final zoom = _mapController.zoom - 1;
                        _mapController.move(_mapController.center, zoom);
                      },
                      child: const Icon(Icons.remove),
                    ),
                  ],
                ),
              ),
            
            // زر الموقع الحالي
            if (widget.showCurrentLocation)
              Positioned(
                bottom: 16,
                left: 16,
                child: FloatingActionButton.small(
                  heroTag: "current_location",
                  onPressed: () {
                    _mapController.move(widget.center, widget.zoom);
                  },
                  child: const Icon(Icons.my_location),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class LocationMarker extends StatelessWidget {
  final LatLng position;
  final String? title;
  final String? subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;

  const LocationMarker({
    super.key,
    required this.position,
    this.title,
    this.subtitle,
    this.icon = Icons.location_on,
    this.color = Colors.red,
    this.onTap,
  });

  Marker toMarker() {
    return Marker(
      point: position,
      builder: (context) => GestureDetector(
        onTap: onTap,
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 3),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            ),
            if (title != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Text(
                  title!,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class RouteMapWidget extends StatefulWidget {
  final LatLng startPoint;
  final LatLng endPoint;
  final List<LatLng>? routePoints;
  final String? startTitle;
  final String? endTitle;
  final double? height;

  const RouteMapWidget({
    super.key,
    required this.startPoint,
    required this.endPoint,
    this.routePoints,
    this.startTitle,
    this.endTitle,
    this.height,
  });

  @override
  State<RouteMapWidget> createState() => _RouteMapWidgetState();
}

class _RouteMapWidgetState extends State<RouteMapWidget> {
  late LatLng _center;
  late double _zoom;

  @override
  void initState() {
    super.initState();
    _calculateCenterAndZoom();
  }

  void _calculateCenterAndZoom() {
    final lat1 = widget.startPoint.latitude;
    final lon1 = widget.startPoint.longitude;
    final lat2 = widget.endPoint.latitude;
    final lon2 = widget.endPoint.longitude;

    _center = LatLng(
      (lat1 + lat2) / 2,
      (lon1 + lon2) / 2,
    );

    // حساب التكبير المناسب بناءً على المسافة
    final distance = const Distance().as(LengthUnit.Kilometer, widget.startPoint, widget.endPoint);
    if (distance < 1) {
      _zoom = 16;
    } else if (distance < 5) {
      _zoom = 14;
    } else if (distance < 20) {
      _zoom = 12;
    } else {
      _zoom = 10;
    }
  }

  @override
  Widget build(BuildContext context) {
    final markers = [
      LocationMarker(
        position: widget.startPoint,
        title: widget.startTitle ?? 'البداية',
        icon: Icons.radio_button_checked,
        color: Colors.green,
      ).toMarker(),
      LocationMarker(
        position: widget.endPoint,
        title: widget.endTitle ?? 'الوجهة',
        icon: Icons.location_on,
        color: Colors.red,
      ).toMarker(),
    ];

    return CustomMapWidget(
      center: _center,
      zoom: _zoom,
      markers: markers,
      height: widget.height,
      showCurrentLocation: false,
    );
  }
}

class MapSearchWidget extends StatefulWidget {
  final Function(String)? onSearch;
  final Function(LatLng)? onLocationSelected;
  final String hintText;

  const MapSearchWidget({
    super.key,
    this.onSearch,
    this.onLocationSelected,
    this.hintText = 'ابحث عن موقع...',
  });

  @override
  State<MapSearchWidget> createState() => _MapSearchWidgetState();
}

class _MapSearchWidgetState extends State<MapSearchWidget> {
  final _searchController = TextEditingController();
  final _focusNode = FocusNode();
  bool _isSearching = false;
  List<SearchResult> _searchResults = [];

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: _searchController,
            focusNode: _focusNode,
            decoration: InputDecoration(
              hintText: widget.hintText,
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _isSearching
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            setState(() {
                              _searchResults.clear();
                            });
                          },
                        )
                      : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            onChanged: (value) {
              if (value.isNotEmpty) {
                _performSearch(value);
              } else {
                setState(() {
                  _searchResults.clear();
                });
              }
            },
          ),
        ),
        
        if (_searchResults.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _searchResults.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final result = _searchResults[index];
                return ListTile(
                  leading: const Icon(Icons.location_on),
                  title: Text(result.title),
                  subtitle: Text(result.subtitle),
                  onTap: () {
                    _searchController.text = result.title;
                    _focusNode.unfocus();
                    setState(() {
                      _searchResults.clear();
                    });
                    widget.onLocationSelected?.call(result.location);
                  },
                );
              },
            ),
          ),
      ],
    );
  }

  void _performSearch(String query) async {
    setState(() {
      _isSearching = true;
    });

    // محاكاة البحث - في التطبيق الحقيقي ستستخدم geocoding API
    await Future.delayed(const Duration(milliseconds: 500));

    final results = [
      SearchResult(
        title: 'الرياض، المملكة العربية السعودية',
        subtitle: 'العاصمة',
        location: const LatLng(24.7136, 46.6753),
      ),
      SearchResult(
        title: 'جدة، المملكة العربية السعودية',
        subtitle: 'مدينة ساحلية',
        location: const LatLng(21.4858, 39.1925),
      ),
      SearchResult(
        title: 'الدمام، المملكة العربية السعودية',
        subtitle: 'المنطقة الشرقية',
        location: const LatLng(26.4207, 50.0888),
      ),
    ].where((result) => 
        result.title.toLowerCase().contains(query.toLowerCase()) ||
        result.subtitle.toLowerCase().contains(query.toLowerCase())
    ).toList();

    setState(() {
      _searchResults = results;
      _isSearching = false;
    });
  }
}

class SearchResult {
  final String title;
  final String subtitle;
  final LatLng location;

  SearchResult({
    required this.title,
    required this.subtitle,
    required this.location,
  });
}

class MapInfoWindow extends StatelessWidget {
  final String title;
  final String? subtitle;
  final String? imageUrl;
  final VoidCallback? onTap;
  final List<Widget>? actions;

  const MapInfoWindow({
    super.key,
    required this.title,
    this.subtitle,
    this.imageUrl,
    this.onTap,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 250,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (imageUrl != null) ...[
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  imageUrl!,
                  height: 100,
                  width: double.infinity,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(height: 8),
            ],
            
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            if (subtitle != null) ...[
              const SizedBox(height: 4),
              Text(
                subtitle!,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
            
            if (actions != null) ...[
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: actions!,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
