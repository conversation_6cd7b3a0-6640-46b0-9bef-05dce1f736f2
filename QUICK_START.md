# 🚀 دليل البدء السريع - تطبيق الخدمات المتعددة

## 📋 المتطلبات الأساسية

### 1. تثبيت Flutter
```bash
# تحميل Flutter SDK من الموقع الرسمي
https://flutter.dev/docs/get-started/install

# إضافة Flutter إلى PATH
export PATH="$PATH:`pwd`/flutter/bin"

# التحقق من التثبيت
flutter doctor
```

### 2. إعداد البيئة
```bash
# تثبيت Android Studio (للأندرويد)
# تثبيت Xcode (لـ iOS - ماك فقط)
# تثبيت VS Code أو Android Studio للتطوير
```

---

## ⚡ تشغيل سريع

### الطريقة الأولى: استخدام ملفات التشغيل التلقائي

#### على Windows:
```cmd
# انقر مرتين على الملف أو شغله من Command Prompt
run_app.bat
```

#### على Mac/Linux:
```bash
# شغل الملف من Terminal
./run_app.sh
```

### الطريقة الثانية: الأوامر اليدوية

```bash
# 1. تثبيت المكتبات
flutter pub get

# 2. تشغيل على الموبايل
flutter run

# 3. تشغيل على الويب
flutter run -d web-server --web-port 8080

# 4. تشغيل على سطح المكتب
flutter run -d windows  # Windows
flutter run -d macos    # macOS
flutter run -d linux    # Linux
```

---

## 🌐 تشغيل على الويب

### تشغيل محلي
```bash
flutter run -d web-server --web-port 8080
```
ثم افتح: `http://localhost:8080`

### تشغيل مع إمكانية الوصول من الشبكة
```bash
flutter run -d web-server --web-port 8080 --web-hostname 0.0.0.0
```

### بناء للإنتاج
```bash
flutter build web --release
```

---

## 📱 تشغيل على الموبايل

### Android
```bash
# تأكد من تشغيل جهاز أو محاكي
flutter devices

# تشغيل التطبيق
flutter run -d android
```

### iOS (ماك فقط)
```bash
# تأكد من تشغيل محاكي iOS
open -a Simulator

# تشغيل التطبيق
flutter run -d ios
```

---

## 🖥️ تشغيل على سطح المكتب

### Windows
```bash
# تفعيل دعم Windows
flutter config --enable-windows-desktop

# تشغيل التطبيق
flutter run -d windows
```

### macOS
```bash
# تفعيل دعم macOS
flutter config --enable-macos-desktop

# تشغيل التطبيق
flutter run -d macos
```

### Linux
```bash
# تفعيل دعم Linux
flutter config --enable-linux-desktop

# تثبيت المكتبات المطلوبة (Ubuntu/Debian)
sudo apt-get install clang cmake ninja-build pkg-config libgtk-3-dev

# تشغيل التطبيق
flutter run -d linux
```

---

## 🔧 حل المشاكل الشائعة

### مشكلة: Flutter غير معروف
```bash
# تأكد من إضافة Flutter إلى PATH
echo $PATH  # Mac/Linux
echo %PATH% # Windows

# أعد تشغيل Terminal/Command Prompt
```

### مشكلة: لا توجد أجهزة متاحة
```bash
# تحقق من الأجهزة المتصلة
flutter devices

# للأندرويد: تأكد من تفعيل USB Debugging
# لـ iOS: تأكد من تشغيل Simulator
```

### مشكلة: فشل في تثبيت المكتبات
```bash
# نظف المشروع وأعد المحاولة
flutter clean
flutter pub get

# تحديث Flutter
flutter upgrade
```

### مشكلة: أخطاء في البناء
```bash
# نظف وأعد البناء
flutter clean
flutter pub get
flutter pub deps

# إعادة إنشاء ملفات الكود المولدة
flutter packages pub run build_runner build --delete-conflicting-outputs
```

---

## 📊 أوامر مفيدة

### معلومات المشروع
```bash
# معلومات Flutter
flutter --version
flutter doctor -v

# معلومات المشروع
flutter analyze
flutter test
```

### إدارة المكتبات
```bash
# تثبيت مكتبة جديدة
flutter pub add package_name

# إزالة مكتبة
flutter pub remove package_name

# تحديث المكتبات
flutter pub upgrade
```

### البناء للإنتاج
```bash
# Android APK
flutter build apk --release

# Android App Bundle
flutter build appbundle --release

# iOS
flutter build ios --release

# Web
flutter build web --release

# Windows
flutter build windows --release
```

---

## 🎯 نصائح للتطوير

### 1. Hot Reload
- اضغط `r` في Terminal لإعادة التحميل السريع
- اضغط `R` لإعادة التشغيل الكامل
- اضغط `q` للخروج

### 2. تصحيح الأخطاء
```bash
# تشغيل في وضع التصحيح
flutter run --debug

# تشغيل مع معلومات إضافية
flutter run --verbose
```

### 3. اختبار الأداء
```bash
# تشغيل في وضع الإنتاج
flutter run --release

# قياس الأداء
flutter run --profile
```

---

## 📱 اختبار على أجهزة مختلفة

### محاكيات Android
```bash
# إنشاء محاكي جديد
flutter emulators --create

# تشغيل محاكي
flutter emulators --launch emulator_id
```

### محاكيات iOS
```bash
# عرض المحاكيات المتاحة
xcrun simctl list devices

# تشغيل محاكي
open -a Simulator
```

---

## 🌟 مميزات التطبيق

### الأقسام الرئيسية
- 🏨 **الفنادق**: بحث وحجز الفنادق
- 🏠 **العقارات**: تصفح وإضافة العقارات
- 🚕 **التكسي**: طلب رحلات التكسي
- 👤 **الملف الشخصي**: إدارة الحساب والإعدادات

### المميزات المتقدمة
- 🗺️ خرائط مجانية (OpenStreetMap)
- 🔔 نظام إشعارات ذكي
- ⭐ تقييمات تفاعلية
- 🎨 واجهة عربية كاملة
- 📱 تصميم متجاوب

---

## 📞 الدعم

### في حالة وجود مشاكل:
1. تحقق من [FEATURES.md](FEATURES.md) للمميزات الكاملة
2. راجع [README_ENHANCED.md](README_ENHANCED.md) للوثائق التفصيلية
3. ابحث في GitHub Issues للمشاكل المشابهة
4. أنشئ Issue جديد مع تفاصيل المشكلة

### معلومات مفيدة للدعم:
```bash
# معلومات النظام
flutter doctor -v

# معلومات المشروع
flutter analyze

# سجل الأخطاء
flutter logs
```

---

## 🎉 استمتع بالتطوير!

الآن أنت جاهز لتشغيل واستكشاف تطبيق الخدمات المتعددة. التطبيق يحتوي على مميزات متقدمة وواجهة عربية كاملة مع تصميم عصري.

**نصيحة**: ابدأ بتشغيل التطبيق على الويب للحصول على نظرة سريعة، ثم جرب المنصات الأخرى حسب احتياجاتك.
