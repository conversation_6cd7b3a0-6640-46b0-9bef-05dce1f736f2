import 'package:flutter/material.dart';
import '../models/user.dart';

class UserProvider with ChangeNotifier {
  User? _currentUser;
  bool _isLoading = false;
  String? _error;
  bool _hasUnreadNotifications = false;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _currentUser != null;
  bool get hasUnreadNotifications => _hasUnreadNotifications;

  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  // تسجيل الدخول
  Future<bool> login(String email, String password) async {
    try {
      setLoading(true);
      clearError();

      // محاكاة API call
      await Future.delayed(const Duration(seconds: 2));

      // مستخدم تجريبي
      _currentUser = User(
        id: '1',
        name: 'أحمد محمد',
        email: email,
        phone: '+966501234567',
        userType: 'passenger',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );

      setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      setError('خطأ في تسجيل الدخول: ${e.toString()}');
      setLoading(false);
      return false;
    }
  }

  // تسجيل مستخدم جديد
  Future<bool> register({
    required String name,
    required String email,
    required String phone,
    required String password,
    required String userType,
  }) async {
    try {
      setLoading(true);
      clearError();

      // محاكاة API call
      await Future.delayed(const Duration(seconds: 2));

      _currentUser = User(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        email: email,
        phone: phone,
        userType: userType,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );

      setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      setError('خطأ في التسجيل: ${e.toString()}');
      setLoading(false);
      return false;
    }
  }

  // تسجيل الخروج
  void logout() {
    _currentUser = null;
    clearError();
    notifyListeners();
  }

  // تحديث الملف الشخصي
  Future<bool> updateProfile({
    String? name,
    String? phone,
    String? profileImage,
  }) async {
    if (_currentUser == null) return false;

    try {
      setLoading(true);
      clearError();

      // محاكاة API call
      await Future.delayed(const Duration(seconds: 1));

      _currentUser = _currentUser!.copyWith(
        name: name ?? _currentUser!.name,
        phone: phone ?? _currentUser!.phone,
        profileImage: profileImage ?? _currentUser!.profileImage,
      );

      setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      setError('خطأ في تحديث الملف الشخصي: ${e.toString()}');
      setLoading(false);
      return false;
    }
  }

  // تحميل بيانات المستخدم من التخزين المحلي
  Future<void> loadUserFromStorage() async {
    try {
      setLoading(true);

      // محاكاة تحميل من SharedPreferences
      await Future.delayed(const Duration(seconds: 1));

      // يمكن إضافة منطق تحميل المستخدم المحفوظ هنا

      setLoading(false);
    } catch (e) {
      setError('خطأ في تحميل بيانات المستخدم');
      setLoading(false);
    }
  }

  void logout() {
    _currentUser = null;
    _hasUnreadNotifications = false;
    notifyListeners();
  }

  void setUnreadNotifications(bool hasUnread) {
    _hasUnreadNotifications = hasUnread;
    notifyListeners();
  }

  void markNotificationsAsRead() {
    _hasUnreadNotifications = false;
    notifyListeners();
  }
}
