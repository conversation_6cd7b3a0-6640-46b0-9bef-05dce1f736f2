import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:geolocator/geolocator.dart';
import '../../providers/taxi_provider.dart';
import '../../providers/user_provider.dart';
import '../../widgets/ride_request_bottom_sheet.dart';
import '../../widgets/current_ride_widget.dart';
import 'ride_history_screen.dart';

class TaxiMainScreen extends StatefulWidget {
  const TaxiMainScreen({super.key});

  @override
  State<TaxiMainScreen> createState() => _TaxiMainScreenState();
}

class _TaxiMainScreenState extends State<TaxiMainScreen> {
  final MapController _mapController = MapController();
  LatLng? _currentLocation;
  LatLng? _destinationLocation;
  bool _isLoadingLocation = false;

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      // التحقق من الأذونات
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('تم رفض إذن الموقع');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('تم رفض إذن الموقع نهائياً');
      }

      // الحصول على الموقع الحالي
      final Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      setState(() {
        _currentLocation = LatLng(position.latitude, position.longitude);
        _isLoadingLocation = false;
      });

      // تحميل السائقين المتاحين
      if (mounted) {
        context.read<TaxiProvider>().loadAvailableDrivers(
          position.latitude,
          position.longitude,
        );
      }

      // تحريك الخريطة للموقع الحالي
      _mapController.move(_currentLocation!, 15.0);
    } catch (e) {
      setState(() {
        _isLoadingLocation = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديد الموقع: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }

      // استخدام موقع افتراضي (الرياض)
      setState(() {
        _currentLocation = const LatLng(24.7136, 46.6753);
      });
      _mapController.move(_currentLocation!, 12.0);
    }
  }

  void _onMapTap(TapPosition tapPosition, LatLng point) {
    setState(() {
      _destinationLocation = point;
    });
  }

  void _showRideRequestBottomSheet() {
    if (_currentLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى انتظار تحديد موقعك الحالي'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (_destinationLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار وجهة على الخريطة'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => RideRequestBottomSheet(
        pickupLocation: _currentLocation!,
        destinationLocation: _destinationLocation!,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التكسي'),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const RideHistoryScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.my_location),
            onPressed: _getCurrentLocation,
          ),
        ],
      ),
      body: Consumer<TaxiProvider>(
        builder: (context, taxiProvider, child) {
          return Stack(
            children: [
              // الخريطة
              FlutterMap(
                mapController: _mapController,
                options: MapOptions(
                  center: _currentLocation ?? const LatLng(24.7136, 46.6753),
                  zoom: _currentLocation != null ? 15.0 : 12.0,
                  onTap: _onMapTap,
                ),
                children: [
                  // طبقة الخريطة (OpenStreetMap)
                  TileLayer(
                    urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                    userAgentPackageName: 'com.example.multi_service_app',
                  ),
                  
                  // العلامات
                  MarkerLayer(
                    markers: [
                      // الموقع الحالي
                      if (_currentLocation != null)
                        Marker(
                          point: _currentLocation!,
                          builder: (context) => Container(
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.white, width: 3),
                            ),
                            child: const Icon(
                              Icons.person,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ),
                      
                      // الوجهة
                      if (_destinationLocation != null)
                        Marker(
                          point: _destinationLocation!,
                          builder: (context) => Container(
                            decoration: BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.white, width: 3),
                            ),
                            child: const Icon(
                              Icons.location_on,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ),
                      
                      // السائقين المتاحين
                      ...taxiProvider.availableDrivers.map((driver) => Marker(
                        point: LatLng(driver.currentLatitude, driver.currentLongitude),
                        builder: (context) => GestureDetector(
                          onTap: () {
                            showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: Text(driver.name),
                                content: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('السيارة: ${driver.vehicleModel}'),
                                    Text('اللون: ${driver.vehicleColor}'),
                                    Text('اللوحة: ${driver.plateNumber}'),
                                    Text('التقييم: ${driver.rating.toStringAsFixed(1)} ⭐'),
                                    Text('عدد الرحلات: ${driver.totalRides}'),
                                  ],
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    child: const Text('إغلاق'),
                                  ),
                                ],
                              ),
                            );
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.white, width: 2),
                            ),
                            child: const Icon(
                              Icons.local_taxi,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      )),
                    ],
                  ),
                ],
              ),

              // مؤشر التحميل
              if (_isLoadingLocation || taxiProvider.isLoading)
                Container(
                  color: Colors.black26,
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),

              // الرحلة الحالية
              if (taxiProvider.currentRide != null)
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: CurrentRideWidget(ride: taxiProvider.currentRide!),
                ),

              // أزرار التحكم
              if (taxiProvider.currentRide == null)
                Positioned(
                  bottom: 100,
                  left: 16,
                  right: 16,
                  child: Column(
                    children: [
                      // معلومات الوجهة
                      if (_destinationLocation != null)
                        Container(
                          padding: const EdgeInsets.all(16),
                          margin: const EdgeInsets.only(bottom: 16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.location_on, color: Colors.red),
                              const SizedBox(width: 8),
                              const Expanded(
                                child: Text(
                                  'الوجهة المحددة',
                                  style: TextStyle(fontWeight: FontWeight.w500),
                                ),
                              ),
                              TextButton(
                                onPressed: () {
                                  setState(() {
                                    _destinationLocation = null;
                                  });
                                },
                                child: const Text('إلغاء'),
                              ),
                            ],
                          ),
                        ),

                      // زر طلب الرحلة
                      SizedBox(
                        width: double.infinity,
                        height: 50,
                        child: ElevatedButton.icon(
                          onPressed: _showRideRequestBottomSheet,
                          icon: const Icon(Icons.local_taxi),
                          label: const Text(
                            'طلب رحلة',
                            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
