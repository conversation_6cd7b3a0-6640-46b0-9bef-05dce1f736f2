# تطبيق الخدمات المتعددة - Multi Service App

تطبيق شامل يجمع بين ثلاث خدمات رئيسية: حجز الفنادق، العقارات، وخدمة التكسي. مطور باستخدام Flutter مع واجهة باللغة العربية.

## المميزات الرئيسية

### 🏨 قسم الفنادق
- **البحث والفلترة المتقدمة**: بحث حسب المدينة، النجوم، السعر، نوع الفندق
- **عرض تفصيلي**: صور متعددة، تقييمات، مرافق، موقع على الخريطة
- **نظام الحجز**: حجز الغرف مع تحديد التواريخ وعدد الضيوف
- **إدارة الحجوزات**: عرض الحجوزات الحالية والسابقة
- **المفضلة**: إضافة الفنادق للمفضلة
- **الفنادق المميزة**: عرض خاص للفنادق المدفوعة

### 🏠 قسم العقارات
- **تصنيفات متعددة**: 
  - نوع الإعلان: شراء / إيجار
  - الفئات: سكني / تجاري / زراعي
  - الفئات الفرعية: طابو / زراعي
- **البحث المتقدم**: فلترة حسب السعر، المساحة، عدد الغرف، المدينة
- **نظام الإعجاب**: إضافة إعجاب للعقارات مع عداد
- **إضافة عقارات**: إمكانية المستخدمين لإضافة عقاراتهم
- **معلومات التواصل**: عرض بيانات المالك للتواصل المباشر
- **العقارات المميزة**: نظام ترويج مدفوع

### 🚕 قسم التكسي
- **تحديد الموقع**: GPS تلقائي مع إمكانية اختيار الوجهة على الخريطة
- **أنواع الرحلات**: فردية، جماعية، مريحة، فاخرة
- **حساب تقديري**: السعر والمسافة والوقت المتوقع
- **تتبع مباشر**: متابعة موقع السائق في الوقت الفعلي
- **إدارة الرحلات**: طلب، قبول، بدء، إنهاء الرحلة
- **نظام التقييم**: تقييم السائق والرحلة
- **تاريخ الرحلات**: عرض الرحلات السابقة والملغية

## التقنيات المستخدمة

### Frontend
- **Flutter**: إطار العمل الرئيسي
- **Provider**: إدارة الحالة
- **flutter_map**: خرائط مجانية (OpenStreetMap)
- **geolocator**: تحديد الموقع
- **http/dio**: التواصل مع APIs

### الخرائط
- **OpenStreetMap**: خرائط مجانية بدلاً من Google Maps
- **flutter_map**: مكتبة عرض الخرائط
- **latlong2**: التعامل مع الإحداثيات

### التخزين والبيانات
- **SharedPreferences**: تخزين محلي للإعدادات
- **SQLite**: قاعدة بيانات محلية
- **Provider**: إدارة الحالة العامة

## بنية المشروع

```
lib/
├── main.dart                 # نقطة البداية
├── models/                   # نماذج البيانات
│   ├── user.dart
│   ├── hotel.dart
│   ├── real_estate.dart
│   └── taxi.dart
├── providers/                # مقدمي الخدمات (State Management)
│   ├── user_provider.dart
│   ├── hotels_provider.dart
│   ├── real_estate_provider.dart
│   └── taxi_provider.dart
├── screens/                  # الشاشات
│   ├── main_screen.dart
│   ├── hotels/
│   ├── real_estate/
│   ├── taxi/
│   └── profile/
└── widgets/                  # المكونات المشتركة
    ├── hotel_card.dart
    ├── real_estate_card.dart
    ├── search_bar_widget.dart
    └── ...
```

## المتطلبات

- Flutter SDK 3.10.0 أو أحدث
- Dart 3.0.0 أو أحدث
- Android Studio / VS Code
- جهاز Android أو iOS للاختبار

## التثبيت والتشغيل

1. **استنساخ المشروع**:
```bash
git clone [repository-url]
cd multi_service_app
```

2. **تثبيت المكتبات**:
```bash
flutter pub get
```

3. **تشغيل التطبيق**:
```bash
flutter run
```

## الإعدادات المطلوبة

### أذونات Android
تم إضافة الأذونات التالية في `android/app/src/main/AndroidManifest.xml`:
- `INTERNET`: للاتصال بالإنترنت
- `ACCESS_FINE_LOCATION`: لتحديد الموقع الدقيق
- `ACCESS_COARSE_LOCATION`: لتحديد الموقع التقريبي
- `CAMERA`: لالتقاط الصور
- `READ_EXTERNAL_STORAGE`: لقراءة الملفات
- `CALL_PHONE`: للاتصال المباشر

## المميزات المستقبلية

### قريباً
- [ ] نظام الدفع الإلكتروني
- [ ] إشعارات push
- [ ] دردشة مباشرة
- [ ] تقييمات وتعليقات مفصلة
- [ ] نظام النقاط والمكافآت

### متوسط المدى
- [ ] تطبيق منفصل للسائقين
- [ ] لوحة تحكم لأصحاب الفنادق
- [ ] تحليلات وتقارير
- [ ] دعم متعدد اللغات

### طويل المدى
- [ ] ذكاء اصطناعي للتوصيات
- [ ] واقع معزز لعرض العقارات
- [ ] تكامل مع أنظمة الدفع البنكية

## بيانات تجريبية

للاختبار السريع، يمكن استخدام:
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التغييرات مع التوثيق
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## التواصل

للاستفسارات والدعم الفني، يرجى التواصل عبر:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-50-123-4567

---

**ملاحظة**: هذا التطبيق في مرحلة التطوير ويحتوي على بيانات تجريبية. للاستخدام التجاري، يرجى التواصل مع فريق التطوير.
