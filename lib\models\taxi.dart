class TaxiRide {
  final String id;
  final String passengerId;
  final String? driverId;
  final double pickupLatitude;
  final double pickupLongitude;
  final String pickupAddress;
  final double destinationLatitude;
  final double destinationLongitude;
  final String destinationAddress;
  final double estimatedPrice;
  final double? finalPrice;
  final double estimatedDistance;
  final int estimatedDuration; // بالدقائق
  final String rideType; // فردية، جماعية، مريحة، فاخرة
  final String status; // pending, accepted, in_progress, completed, cancelled
  final DateTime createdAt;
  final DateTime? acceptedAt;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final double? passengerRating;
  final double? driverRating;
  final String? notes;

  TaxiRide({
    required this.id,
    required this.passengerId,
    this.driverId,
    required this.pickupLatitude,
    required this.pickupLongitude,
    required this.pickupAddress,
    required this.destinationLatitude,
    required this.destinationLongitude,
    required this.destinationAddress,
    required this.estimatedPrice,
    this.finalPrice,
    required this.estimatedDistance,
    required this.estimatedDuration,
    required this.rideType,
    required this.status,
    required this.createdAt,
    this.acceptedAt,
    this.startedAt,
    this.completedAt,
    this.passengerRating,
    this.driverRating,
    this.notes,
  });

  factory TaxiRide.fromJson(Map<String, dynamic> json) {
    return TaxiRide(
      id: json['id'],
      passengerId: json['passengerId'],
      driverId: json['driverId'],
      pickupLatitude: json['pickupLatitude'].toDouble(),
      pickupLongitude: json['pickupLongitude'].toDouble(),
      pickupAddress: json['pickupAddress'],
      destinationLatitude: json['destinationLatitude'].toDouble(),
      destinationLongitude: json['destinationLongitude'].toDouble(),
      destinationAddress: json['destinationAddress'],
      estimatedPrice: json['estimatedPrice'].toDouble(),
      finalPrice: json['finalPrice']?.toDouble(),
      estimatedDistance: json['estimatedDistance'].toDouble(),
      estimatedDuration: json['estimatedDuration'],
      rideType: json['rideType'],
      status: json['status'],
      createdAt: DateTime.parse(json['createdAt']),
      acceptedAt: json['acceptedAt'] != null ? DateTime.parse(json['acceptedAt']) : null,
      startedAt: json['startedAt'] != null ? DateTime.parse(json['startedAt']) : null,
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
      passengerRating: json['passengerRating']?.toDouble(),
      driverRating: json['driverRating']?.toDouble(),
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'passengerId': passengerId,
      'driverId': driverId,
      'pickupLatitude': pickupLatitude,
      'pickupLongitude': pickupLongitude,
      'pickupAddress': pickupAddress,
      'destinationLatitude': destinationLatitude,
      'destinationLongitude': destinationLongitude,
      'destinationAddress': destinationAddress,
      'estimatedPrice': estimatedPrice,
      'finalPrice': finalPrice,
      'estimatedDistance': estimatedDistance,
      'estimatedDuration': estimatedDuration,
      'rideType': rideType,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'acceptedAt': acceptedAt?.toIso8601String(),
      'startedAt': startedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'passengerRating': passengerRating,
      'driverRating': driverRating,
      'notes': notes,
    };
  }
}

class Driver {
  final String id;
  final String name;
  final String phone;
  final String email;
  final String licenseNumber;
  final String vehicleModel;
  final String vehicleColor;
  final String plateNumber;
  final double currentLatitude;
  final double currentLongitude;
  final bool isAvailable;
  final double rating;
  final int totalRides;
  final String status; // active, inactive, suspended
  final DateTime createdAt;

  Driver({
    required this.id,
    required this.name,
    required this.phone,
    required this.email,
    required this.licenseNumber,
    required this.vehicleModel,
    required this.vehicleColor,
    required this.plateNumber,
    required this.currentLatitude,
    required this.currentLongitude,
    this.isAvailable = true,
    this.rating = 5.0,
    this.totalRides = 0,
    required this.status,
    required this.createdAt,
  });

  factory Driver.fromJson(Map<String, dynamic> json) {
    return Driver(
      id: json['id'],
      name: json['name'],
      phone: json['phone'],
      email: json['email'],
      licenseNumber: json['licenseNumber'],
      vehicleModel: json['vehicleModel'],
      vehicleColor: json['vehicleColor'],
      plateNumber: json['plateNumber'],
      currentLatitude: json['currentLatitude'].toDouble(),
      currentLongitude: json['currentLongitude'].toDouble(),
      isAvailable: json['isAvailable'] ?? true,
      rating: json['rating']?.toDouble() ?? 5.0,
      totalRides: json['totalRides'] ?? 0,
      status: json['status'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'licenseNumber': licenseNumber,
      'vehicleModel': vehicleModel,
      'vehicleColor': vehicleColor,
      'plateNumber': plateNumber,
      'currentLatitude': currentLatitude,
      'currentLongitude': currentLongitude,
      'isAvailable': isAvailable,
      'rating': rating,
      'totalRides': totalRides,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}
