#!/usr/bin/env python3
"""
خادم ويب بسيط لتشغيل تطبيق الخدمات المتعددة
Simple web server for Multi Service App
"""

import http.server
import socketserver
import os
import webbrowser
import threading
import time

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        # إضافة headers للدعم العربي والأمان
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.send_header('Cache-Control', 'no-cache')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_GET(self):
        # توجيه الطلبات
        if self.path == '/' or self.path == '/index.html':
            self.path = '/demo.html'
        elif self.path == '/app':
            self.path = '/web/index.html'
        
        return super().do_GET()
    
    def log_message(self, format, *args):
        # تخصيص رسائل السجل
        print(f"🌐 {self.address_string()} - {format % args}")

def open_browser_delayed(url, delay=2):
    """فتح المتصفح بعد تأخير"""
    time.sleep(delay)
    webbrowser.open(url)

def start_server(port=8080):
    """بدء الخادم"""
    try:
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            print("=" * 60)
            print("🚀 تطبيق الخدمات المتعددة - خادم الويب")
            print("=" * 60)
            print(f"📡 الخادم يعمل على المنفذ: {port}")
            print(f"🌐 رابط التطبيق: http://localhost:{port}")
            print(f"📱 العرض التجريبي: http://localhost:{port}/demo.html")
            print(f"🔧 التطبيق الكامل: http://localhost:{port}/app")
            print("=" * 60)
            print("💡 نصائح:")
            print("   - اضغط Ctrl+C لإيقاف الخادم")
            print("   - استخدم F5 لتحديث الصفحة")
            print("   - افتح أدوات المطور (F12) لمحاكاة الموبايل")
            print("=" * 60)
            
            # فتح المتصفح تلقائياً
            browser_thread = threading.Thread(
                target=open_browser_delayed, 
                args=(f"http://localhost:{port}",)
            )
            browser_thread.daemon = True
            browser_thread.start()
            
            print("🔄 جاري فتح المتصفح...")
            print("⏳ انتظار الطلبات...")
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n" + "=" * 60)
        print("🛑 تم إيقاف الخادم بواسطة المستخدم")
        print("👋 شكراً لاستخدام تطبيق الخدمات المتعددة!")
        print("=" * 60)
    except OSError as e:
        if e.errno == 98:  # Address already in use
            print(f"❌ خطأ: المنفذ {port} مستخدم بالفعل")
            print(f"💡 جرب منفذ آخر: python server.py {port + 1}")
        else:
            print(f"❌ خطأ في بدء الخادم: {e}")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")

if __name__ == "__main__":
    import sys
    
    # تحديد المنفذ من المعاملات
    port = 8080
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("❌ رقم المنفذ غير صحيح، سيتم استخدام المنفذ الافتراضي 8080")
    
    start_server(port)
