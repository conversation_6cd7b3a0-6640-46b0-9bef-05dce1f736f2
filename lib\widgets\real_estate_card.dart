import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/real_estate.dart';
import '../providers/real_estate_provider.dart';
import '../providers/user_provider.dart';

class RealEstateCard extends StatelessWidget {
  final RealEstate property;
  final bool isFeatured;

  const RealEstateCard({
    super.key,
    required this.property,
    this.isFeatured = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isFeatured ? 8 : 4,
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: () {
          // التنقل لتفاصيل العقار
          _navigateToPropertyDetails(context);
        },
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة العقار
            Stack(
              children: [
                Container(
                  height: isFeatured ? 120 : 160,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(12),
                    ),
                    color: Colors.grey[200],
                  ),
                  child: property.images.isNotEmpty
                      ? ClipRRect(
                          borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(12),
                          ),
                          child: Image.network(
                            property.images.first,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey[200],
                                child: Icon(
                                  Icons.home_work,
                                  size: 48,
                                  color: Colors.grey[400],
                                ),
                              );
                            },
                          ),
                        )
                      : Icon(
                          Icons.home_work,
                          size: 48,
                          color: Colors.grey[400],
                        ),
                ),

                // شارة مميز
                if (property.isFeatured)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.amber,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        '⭐ مميز',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                // نوع الإعلان
                Positioned(
                  top: 8,
                  left: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: property.listingType == 'شراء' 
                          ? Colors.green 
                          : Colors.blue,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      property.listingType,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),

                // زر الإعجاب
                Positioned(
                  bottom: 8,
                  left: 8,
                  child: Consumer2<RealEstateProvider, UserProvider>(
                    builder: (context, realEstateProvider, userProvider, child) {
                      if (!userProvider.isLoggedIn) return const SizedBox.shrink();
                      
                      final isLiked = realEstateProvider.isLiked(
                        property.id, 
                        userProvider.currentUser!.id,
                      );
                      
                      return GestureDetector(
                        onTap: () {
                          realEstateProvider.toggleLike(
                            property.id,
                            userProvider.currentUser!.id,
                          );
                        },
                        child: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.9),
                            shape: BoxShape.circle,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                isLiked ? Icons.favorite : Icons.favorite_border,
                                color: isLiked ? Colors.red : Colors.grey[600],
                                size: 16,
                              ),
                              if (property.likesCount > 0) ...[
                                const SizedBox(width: 4),
                                Text(
                                  '${property.likesCount}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[700],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),

            // معلومات العقار
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان العقار والفئة
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          property.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: _getCategoryColor(property.category),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          property.category,
                          style: const TextStyle(
                            fontSize: 10,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 4),

                  // الموقع
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 14,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          property.address,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // تفاصيل العقار
                  Row(
                    children: [
                      // المساحة
                      _buildPropertyDetail(
                        Icons.square_foot,
                        '${property.area.toStringAsFixed(0)} م²',
                      ),
                      
                      if (property.bedrooms != null) ...[
                        const SizedBox(width: 12),
                        _buildPropertyDetail(
                          Icons.bed,
                          '${property.bedrooms} غرف',
                        ),
                      ],
                      
                      if (property.bathrooms != null) ...[
                        const SizedBox(width: 12),
                        _buildPropertyDetail(
                          Icons.bathroom,
                          '${property.bathrooms} حمام',
                        ),
                      ],
                    ],
                  ),

                  const SizedBox(height: 8),

                  // السعر ومعلومات التواصل
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${property.price.toStringAsFixed(0)} ر.س',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                          if (property.listingType == 'إيجار')
                            Text(
                              'شهرياً',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                        ],
                      ),
                      
                      // زر الاتصال
                      ElevatedButton.icon(
                        onPressed: () {
                          _showContactDialog(context);
                        },
                        icon: const Icon(Icons.phone, size: 16),
                        label: const Text('اتصال'),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          minimumSize: Size.zero,
                        ),
                      ),
                    ],
                  ),

                  if (!isFeatured && property.subCategory.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    
                    // الفئة الفرعية
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        property.subCategory,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyDetail(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'سكني':
        return Colors.blue;
      case 'تجاري':
        return Colors.orange;
      case 'زراعي':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  void _navigateToPropertyDetails(BuildContext context) {
    // TODO: تنفيذ التنقل لتفاصيل العقار
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('عرض تفاصيل ${property.title}'),
      ),
    );
  }

  void _showContactDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات التواصل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الاسم: ${property.contactName}'),
            const SizedBox(height: 8),
            Text('الهاتف: ${property.contactPhone}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: تنفيذ الاتصال
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('الاتصال بـ ${property.contactPhone}'),
                ),
              );
            },
            child: const Text('اتصال'),
          ),
        ],
      ),
    );
  }
}
