import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:latlong2/latlong.dart';
import '../providers/taxi_provider.dart';
import '../providers/user_provider.dart';

class RideRequestBottomSheet extends StatefulWidget {
  final LatLng pickupLocation;
  final LatLng destinationLocation;

  const RideRequestBottomSheet({
    super.key,
    required this.pickupLocation,
    required this.destinationLocation,
  });

  @override
  State<RideRequestBottomSheet> createState() => _RideRequestBottomSheetState();
}

class _RideRequestBottomSheetState extends State<RideRequestBottomSheet> {
  String selectedRideType = 'فردية';
  final List<String> rideTypes = ['فردية', 'جماعية', 'مريحة', 'فاخرة'];
  
  // أسعار تقديرية
  final Map<String, double> basePrices = {
    'فردية': 15.0,
    'جماعية': 12.0,
    'مريحة': 22.0,
    'فاخرة': 35.0,
  };

  double _calculateEstimatedPrice() {
    // حساب المسافة التقديرية
    const double earthRadius = 6371;
    final double dLat = (widget.destinationLocation.latitude - widget.pickupLocation.latitude) * (3.14159 / 180);
    final double dLon = (widget.destinationLocation.longitude - widget.pickupLocation.longitude) * (3.14159 / 180);
    
    final double a = (dLat / 2) * (dLat / 2) +
        (widget.pickupLocation.latitude * (3.14159 / 180)) * (widget.destinationLocation.latitude * (3.14159 / 180)) *
        (dLon / 2) * (dLon / 2);
    
    final double c = 2 * (a.sqrt());
    final double distance = earthRadius * c;

    final double basePrice = basePrices[selectedRideType] ?? 15.0;
    return basePrice + (distance * 2.5);
  }

  int _calculateEstimatedTime() {
    // حساب الوقت التقديري (40 كم/ساعة متوسط)
    const double earthRadius = 6371;
    final double dLat = (widget.destinationLocation.latitude - widget.pickupLocation.latitude) * (3.14159 / 180);
    final double dLon = (widget.destinationLocation.longitude - widget.pickupLocation.longitude) * (3.14159 / 180);
    
    final double a = (dLat / 2) * (dLat / 2) +
        (widget.pickupLocation.latitude * (3.14159 / 180)) * (widget.destinationLocation.latitude * (3.14159 / 180)) *
        (dLon / 2) * (dLon / 2);
    
    final double c = 2 * (a.sqrt());
    final double distance = earthRadius * c;

    return (distance / 40 * 60).round(); // بالدقائق
  }

  @override
  Widget build(BuildContext context) {
    final estimatedPrice = _calculateEstimatedPrice();
    final estimatedTime = _calculateEstimatedTime();

    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.8,
        expand: false,
        builder: (context, scrollController) {
          return SingleChildScrollView(
            controller: scrollController,
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // مؤشر السحب
                  Center(
                    child: Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),

                  // العنوان
                  const Text(
                    'تأكيد طلب الرحلة',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 20),

                  // معلومات الرحلة
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Container(
                              width: 12,
                              height: 12,
                              decoration: const BoxDecoration(
                                color: Colors.blue,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 12),
                            const Expanded(
                              child: Text(
                                'موقعك الحالي',
                                style: TextStyle(fontWeight: FontWeight.w500),
                              ),
                            ),
                          ],
                        ),
                        
                        Container(
                          margin: const EdgeInsets.only(left: 6, top: 8, bottom: 8),
                          child: const Row(
                            children: [
                              SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  children: [
                                    Icon(Icons.more_vert, color: Colors.grey),
                                    Icon(Icons.more_vert, color: Colors.grey),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        Row(
                          children: [
                            Container(
                              width: 12,
                              height: 12,
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 12),
                            const Expanded(
                              child: Text(
                                'الوجهة المحددة',
                                style: TextStyle(fontWeight: FontWeight.w500),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 20),

                  // نوع الرحلة
                  const Text(
                    'اختر نوع الرحلة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 12),

                  ...rideTypes.map((type) => _buildRideTypeOption(type)),

                  const SizedBox(height: 20),

                  // معلومات السعر والوقت
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'السعر التقديري',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                            Text(
                              '${estimatedPrice.toStringAsFixed(0)} ر.س',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            const Text(
                              'الوقت التقديري',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                            Text(
                              '$estimatedTime دقيقة',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // زر تأكيد الطلب
                  Consumer2<TaxiProvider, UserProvider>(
                    builder: (context, taxiProvider, userProvider, child) {
                      return SizedBox(
                        width: double.infinity,
                        height: 50,
                        child: ElevatedButton(
                          onPressed: taxiProvider.isLoading || !userProvider.isLoggedIn
                              ? null
                              : () => _requestRide(context, taxiProvider, userProvider),
                          child: taxiProvider.isLoading
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Text(
                                  'تأكيد الطلب',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 16),

                  // ملاحظة
                  Text(
                    '* السعر والوقت تقديريان وقد يختلفان حسب حالة المرور والطريق المسلوك',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRideTypeOption(String type) {
    final isSelected = selectedRideType == type;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedRideType = type;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue[50] : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              _getRideTypeIcon(type),
              color: isSelected ? Colors.blue : Colors.grey[600],
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    type,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: isSelected ? Colors.blue : Colors.black,
                    ),
                  ),
                  Text(
                    _getRideTypeDescription(type),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: Colors.blue,
              ),
          ],
        ),
      ),
    );
  }

  IconData _getRideTypeIcon(String type) {
    switch (type) {
      case 'فردية':
        return Icons.person;
      case 'جماعية':
        return Icons.group;
      case 'مريحة':
        return Icons.airline_seat_recline_extra;
      case 'فاخرة':
        return Icons.star;
      default:
        return Icons.local_taxi;
    }
  }

  String _getRideTypeDescription(String type) {
    switch (type) {
      case 'فردية':
        return 'رحلة عادية لشخص واحد';
      case 'جماعية':
        return 'رحلة مشتركة بسعر أقل';
      case 'مريحة':
        return 'سيارة أكبر وأكثر راحة';
      case 'فاخرة':
        return 'سيارة فاخرة مع خدمة مميزة';
      default:
        return '';
    }
  }

  Future<void> _requestRide(
    BuildContext context,
    TaxiProvider taxiProvider,
    UserProvider userProvider,
  ) async {
    final success = await taxiProvider.requestRide(
      passengerId: userProvider.currentUser!.id,
      pickupLatitude: widget.pickupLocation.latitude,
      pickupLongitude: widget.pickupLocation.longitude,
      pickupAddress: 'موقعك الحالي', // يمكن تحسينه بالحصول على العنوان الفعلي
      destinationLatitude: widget.destinationLocation.latitude,
      destinationLongitude: widget.destinationLocation.longitude,
      destinationAddress: 'الوجهة المحددة', // يمكن تحسينه بالحصول على العنوان الفعلي
      rideType: selectedRideType,
    );

    if (mounted) {
      if (success) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال طلب الرحلة بنجاح! جاري البحث عن سائق...'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(taxiProvider.error ?? 'حدث خطأ في إرسال الطلب'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
