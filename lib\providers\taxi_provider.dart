import 'package:flutter/material.dart';
import '../models/taxi.dart';

class TaxiProvider with ChangeNotifier {
  List<TaxiRide> _rides = [];
  List<Driver> _availableDrivers = [];
  TaxiRide? _currentRide;
  Driver? _currentDriver;
  bool _isLoading = false;
  String? _error;

  List<TaxiRide> get rides => _rides;
  List<Driver> get availableDrivers => _availableDrivers;
  TaxiRide? get currentRide => _currentRide;
  Driver? get currentDriver => _currentDriver;
  bool get isLoading => _isLoading;
  String? get error => _error;

  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // تحميل السائقين المتاحين
  Future<void> loadAvailableDrivers(double latitude, double longitude) async {
    try {
      setLoading(true);
      setError(null);

      // محاكاة API call
      await Future.delayed(const Duration(seconds: 2));

      // بيانات تجريبية للسائقين
      _availableDrivers = [
        Driver(
          id: '1',
          name: 'أحمد محمد',
          phone: '+966501234567',
          email: '<EMAIL>',
          licenseNumber: 'LIC123456',
          vehicleModel: 'تويوتا كامري 2022',
          vehicleColor: 'أبيض',
          plateNumber: 'أ ب ج 1234',
          currentLatitude: latitude + 0.01,
          currentLongitude: longitude + 0.01,
          isAvailable: true,
          rating: 4.8,
          totalRides: 245,
          status: 'active',
          createdAt: DateTime.now().subtract(const Duration(days: 180)),
        ),
        Driver(
          id: '2',
          name: 'محمد علي',
          phone: '+966502345678',
          email: '<EMAIL>',
          licenseNumber: 'LIC789012',
          vehicleModel: 'هيونداي إلنترا 2021',
          vehicleColor: 'أسود',
          plateNumber: 'د هـ و 5678',
          currentLatitude: latitude - 0.02,
          currentLongitude: longitude + 0.015,
          isAvailable: true,
          rating: 4.6,
          totalRides: 189,
          status: 'active',
          createdAt: DateTime.now().subtract(const Duration(days: 120)),
        ),
        Driver(
          id: '3',
          name: 'خالد السعد',
          phone: '+966503456789',
          email: '<EMAIL>',
          licenseNumber: 'LIC345678',
          vehicleModel: 'نيسان التيما 2023',
          vehicleColor: 'فضي',
          plateNumber: 'ز ح ط 9012',
          currentLatitude: latitude + 0.005,
          currentLongitude: longitude - 0.01,
          isAvailable: true,
          rating: 4.9,
          totalRides: 312,
          status: 'active',
          createdAt: DateTime.now().subtract(const Duration(days: 300)),
        ),
      ];

      setLoading(false);
    } catch (e) {
      setError('خطأ في تحميل السائقين: ${e.toString()}');
      setLoading(false);
    }
  }

  // طلب رحلة جديدة
  Future<bool> requestRide({
    required String passengerId,
    required double pickupLatitude,
    required double pickupLongitude,
    required String pickupAddress,
    required double destinationLatitude,
    required double destinationLongitude,
    required String destinationAddress,
    required String rideType,
  }) async {
    try {
      setLoading(true);
      setError(null);

      // حساب المسافة والسعر التقديري
      final distance = _calculateDistance(
        pickupLatitude, pickupLongitude,
        destinationLatitude, destinationLongitude,
      );
      
      final estimatedPrice = _calculatePrice(distance, rideType);
      final estimatedDuration = _calculateDuration(distance);

      // إنشاء طلب الرحلة
      final ride = TaxiRide(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        passengerId: passengerId,
        pickupLatitude: pickupLatitude,
        pickupLongitude: pickupLongitude,
        pickupAddress: pickupAddress,
        destinationLatitude: destinationLatitude,
        destinationLongitude: destinationLongitude,
        destinationAddress: destinationAddress,
        estimatedPrice: estimatedPrice,
        estimatedDistance: distance,
        estimatedDuration: estimatedDuration,
        rideType: rideType,
        status: 'pending',
        createdAt: DateTime.now(),
      );

      // محاكاة API call
      await Future.delayed(const Duration(seconds: 2));

      _currentRide = ride;
      _rides.add(ride);

      setLoading(false);
      notifyListeners();

      // محاكاة قبول السائق بعد 10 ثوان
      _simulateDriverAcceptance();

      return true;
    } catch (e) {
      setError('خطأ في طلب الرحلة: ${e.toString()}');
      setLoading(false);
      return false;
    }
  }

  // محاكاة قبول السائق
  void _simulateDriverAcceptance() {
    Future.delayed(const Duration(seconds: 10), () {
      if (_currentRide != null && _currentRide!.status == 'pending') {
        // اختيار سائق عشوائي
        if (_availableDrivers.isNotEmpty) {
          _currentDriver = _availableDrivers.first;
          
          _currentRide = TaxiRide(
            id: _currentRide!.id,
            passengerId: _currentRide!.passengerId,
            driverId: _currentDriver!.id,
            pickupLatitude: _currentRide!.pickupLatitude,
            pickupLongitude: _currentRide!.pickupLongitude,
            pickupAddress: _currentRide!.pickupAddress,
            destinationLatitude: _currentRide!.destinationLatitude,
            destinationLongitude: _currentRide!.destinationLongitude,
            destinationAddress: _currentRide!.destinationAddress,
            estimatedPrice: _currentRide!.estimatedPrice,
            estimatedDistance: _currentRide!.estimatedDistance,
            estimatedDuration: _currentRide!.estimatedDuration,
            rideType: _currentRide!.rideType,
            status: 'accepted',
            createdAt: _currentRide!.createdAt,
            acceptedAt: DateTime.now(),
          );

          // تحديث الرحلة في القائمة
          final rideIndex = _rides.indexWhere((r) => r.id == _currentRide!.id);
          if (rideIndex != -1) {
            _rides[rideIndex] = _currentRide!;
          }

          notifyListeners();
        }
      }
    });
  }

  // بدء الرحلة
  Future<bool> startRide() async {
    if (_currentRide == null || _currentRide!.status != 'accepted') {
      return false;
    }

    try {
      setLoading(true);

      // محاكاة API call
      await Future.delayed(const Duration(seconds: 1));

      _currentRide = TaxiRide(
        id: _currentRide!.id,
        passengerId: _currentRide!.passengerId,
        driverId: _currentRide!.driverId,
        pickupLatitude: _currentRide!.pickupLatitude,
        pickupLongitude: _currentRide!.pickupLongitude,
        pickupAddress: _currentRide!.pickupAddress,
        destinationLatitude: _currentRide!.destinationLatitude,
        destinationLongitude: _currentRide!.destinationLongitude,
        destinationAddress: _currentRide!.destinationAddress,
        estimatedPrice: _currentRide!.estimatedPrice,
        estimatedDistance: _currentRide!.estimatedDistance,
        estimatedDuration: _currentRide!.estimatedDuration,
        rideType: _currentRide!.rideType,
        status: 'in_progress',
        createdAt: _currentRide!.createdAt,
        acceptedAt: _currentRide!.acceptedAt,
        startedAt: DateTime.now(),
      );

      // تحديث الرحلة في القائمة
      final rideIndex = _rides.indexWhere((r) => r.id == _currentRide!.id);
      if (rideIndex != -1) {
        _rides[rideIndex] = _currentRide!;
      }

      setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      setError('خطأ في بدء الرحلة: ${e.toString()}');
      setLoading(false);
      return false;
    }
  }

  // إنهاء الرحلة
  Future<bool> completeRide(double finalPrice) async {
    if (_currentRide == null || _currentRide!.status != 'in_progress') {
      return false;
    }

    try {
      setLoading(true);

      // محاكاة API call
      await Future.delayed(const Duration(seconds: 1));

      _currentRide = TaxiRide(
        id: _currentRide!.id,
        passengerId: _currentRide!.passengerId,
        driverId: _currentRide!.driverId,
        pickupLatitude: _currentRide!.pickupLatitude,
        pickupLongitude: _currentRide!.pickupLongitude,
        pickupAddress: _currentRide!.pickupAddress,
        destinationLatitude: _currentRide!.destinationLatitude,
        destinationLongitude: _currentRide!.destinationLongitude,
        destinationAddress: _currentRide!.destinationAddress,
        estimatedPrice: _currentRide!.estimatedPrice,
        finalPrice: finalPrice,
        estimatedDistance: _currentRide!.estimatedDistance,
        estimatedDuration: _currentRide!.estimatedDuration,
        rideType: _currentRide!.rideType,
        status: 'completed',
        createdAt: _currentRide!.createdAt,
        acceptedAt: _currentRide!.acceptedAt,
        startedAt: _currentRide!.startedAt,
        completedAt: DateTime.now(),
      );

      // تحديث الرحلة في القائمة
      final rideIndex = _rides.indexWhere((r) => r.id == _currentRide!.id);
      if (rideIndex != -1) {
        _rides[rideIndex] = _currentRide!;
      }

      setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      setError('خطأ في إنهاء الرحلة: ${e.toString()}');
      setLoading(false);
      return false;
    }
  }

  // إلغاء الرحلة
  Future<bool> cancelRide() async {
    if (_currentRide == null) return false;

    try {
      setLoading(true);

      // محاكاة API call
      await Future.delayed(const Duration(seconds: 1));

      _currentRide = TaxiRide(
        id: _currentRide!.id,
        passengerId: _currentRide!.passengerId,
        driverId: _currentRide!.driverId,
        pickupLatitude: _currentRide!.pickupLatitude,
        pickupLongitude: _currentRide!.pickupLongitude,
        pickupAddress: _currentRide!.pickupAddress,
        destinationLatitude: _currentRide!.destinationLatitude,
        destinationLongitude: _currentRide!.destinationLongitude,
        destinationAddress: _currentRide!.destinationAddress,
        estimatedPrice: _currentRide!.estimatedPrice,
        finalPrice: _currentRide!.finalPrice,
        estimatedDistance: _currentRide!.estimatedDistance,
        estimatedDuration: _currentRide!.estimatedDuration,
        rideType: _currentRide!.rideType,
        status: 'cancelled',
        createdAt: _currentRide!.createdAt,
        acceptedAt: _currentRide!.acceptedAt,
        startedAt: _currentRide!.startedAt,
        completedAt: _currentRide!.completedAt,
      );

      // تحديث الرحلة في القائمة
      final rideIndex = _rides.indexWhere((r) => r.id == _currentRide!.id);
      if (rideIndex != -1) {
        _rides[rideIndex] = _currentRide!;
      }

      _currentRide = null;
      _currentDriver = null;

      setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      setError('خطأ في إلغاء الرحلة: ${e.toString()}');
      setLoading(false);
      return false;
    }
  }

  // حساب المسافة (تقريبي)
  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    // حساب تقريبي للمسافة بالكيلومتر
    const double earthRadius = 6371;
    final double dLat = (lat2 - lat1) * (3.14159 / 180);
    final double dLon = (lon2 - lon1) * (3.14159 / 180);

    final double a = (dLat / 2) * (dLat / 2) +
        (lat1 * (3.14159 / 180)) * (lat2 * (3.14159 / 180)) *
        (dLon / 2) * (dLon / 2);

    final double c = 2 * (a.sqrt());
    return earthRadius * c;
  }

  // حساب السعر
  double _calculatePrice(double distance, String rideType) {
    double basePrice = 10.0; // سعر البداية
    double pricePerKm = 2.5;

    switch (rideType) {
      case 'فاخرة':
        pricePerKm *= 2.0;
        break;
      case 'مريحة':
        pricePerKm *= 1.5;
        break;
      case 'جماعية':
        pricePerKm *= 0.8;
        break;
    }

    return basePrice + (distance * pricePerKm);
  }

  // حساب المدة التقديرية
  int _calculateDuration(double distance) {
    // متوسط سرعة 40 كم/ساعة في المدينة
    return (distance / 40 * 60).round();
  }

  // تحميل تاريخ الرحلات
  Future<void> loadRideHistory(String userId) async {
    try {
      setLoading(true);
      setError(null);

      // محاكاة API call
      await Future.delayed(const Duration(seconds: 1));

      // فلترة الرحلات حسب المستخدم
      _rides = _rides.where((r) => r.passengerId == userId).toList();

      setLoading(false);
    } catch (e) {
      setError('خطأ في تحميل تاريخ الرحلات: ${e.toString()}');
      setLoading(false);
    }
  }
}
