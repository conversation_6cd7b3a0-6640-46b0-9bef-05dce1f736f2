import 'package:flutter/material.dart';

class RatingWidget extends StatelessWidget {
  final double rating;
  final int maxRating;
  final double size;
  final Color? activeColor;
  final Color? inactiveColor;
  final bool showText;
  final String? reviewsCount;
  final bool allowHalfRating;

  const RatingWidget({
    super.key,
    required this.rating,
    this.maxRating = 5,
    this.size = 16.0,
    this.activeColor,
    this.inactiveColor,
    this.showText = false,
    this.reviewsCount,
    this.allowHalfRating = true,
  });

  @override
  Widget build(BuildContext context) {
    final activeStarColor = activeColor ?? Colors.amber;
    final inactiveStarColor = inactiveColor ?? Colors.grey[300]!;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(maxRating, (index) {
            final starValue = index + 1;
            
            if (rating >= starValue) {
              // نجمة كاملة
              return Icon(
                Icons.star,
                size: size,
                color: activeStarColor,
              );
            } else if (allowHalfRating && rating >= starValue - 0.5) {
              // نصف نجمة
              return Stack(
                children: [
                  Icon(
                    Icons.star,
                    size: size,
                    color: inactiveStarColor,
                  ),
                  ClipRect(
                    clipper: HalfClipper(),
                    child: Icon(
                      Icons.star,
                      size: size,
                      color: activeStarColor,
                    ),
                  ),
                ],
              );
            } else {
              // نجمة فارغة
              return Icon(
                Icons.star_border,
                size: size,
                color: inactiveStarColor,
              );
            }
          }),
        ),
        if (showText) ...[
          const SizedBox(width: 4),
          Text(
            rating.toStringAsFixed(1),
            style: TextStyle(
              fontSize: size * 0.8,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
        ],
        if (reviewsCount != null) ...[
          const SizedBox(width: 4),
          Text(
            '($reviewsCount)',
            style: TextStyle(
              fontSize: size * 0.7,
              color: Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }
}

class InteractiveRatingWidget extends StatefulWidget {
  final double initialRating;
  final int maxRating;
  final double size;
  final Color? activeColor;
  final Color? inactiveColor;
  final Function(double)? onRatingChanged;
  final bool allowHalfRating;

  const InteractiveRatingWidget({
    super.key,
    this.initialRating = 0.0,
    this.maxRating = 5,
    this.size = 32.0,
    this.activeColor,
    this.inactiveColor,
    this.onRatingChanged,
    this.allowHalfRating = false,
  });

  @override
  State<InteractiveRatingWidget> createState() => _InteractiveRatingWidgetState();
}

class _InteractiveRatingWidgetState extends State<InteractiveRatingWidget> {
  late double _currentRating;

  @override
  void initState() {
    super.initState();
    _currentRating = widget.initialRating;
  }

  @override
  Widget build(BuildContext context) {
    final activeStarColor = widget.activeColor ?? Colors.amber;
    final inactiveStarColor = widget.inactiveColor ?? Colors.grey[300]!;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(widget.maxRating, (index) {
        final starValue = index + 1;
        
        return GestureDetector(
          onTap: () {
            setState(() {
              _currentRating = starValue.toDouble();
            });
            widget.onRatingChanged?.call(_currentRating);
          },
          onPanUpdate: widget.allowHalfRating ? (details) {
            final RenderBox box = context.findRenderObject() as RenderBox;
            final position = box.globalToLocal(details.globalPosition);
            final starWidth = widget.size;
            final starIndex = (position.dx / starWidth).floor();
            final starPosition = (position.dx % starWidth) / starWidth;
            
            if (starIndex >= 0 && starIndex < widget.maxRating) {
              double newRating;
              if (starPosition < 0.5) {
                newRating = starIndex + 0.5;
              } else {
                newRating = starIndex + 1.0;
              }
              
              if (newRating != _currentRating) {
                setState(() {
                  _currentRating = newRating;
                });
                widget.onRatingChanged?.call(_currentRating);
              }
            }
          } : null,
          child: Container(
            padding: const EdgeInsets.all(2),
            child: _buildStar(starValue, activeStarColor, inactiveStarColor),
          ),
        );
      }),
    );
  }

  Widget _buildStar(int starValue, Color activeColor, Color inactiveColor) {
    if (_currentRating >= starValue) {
      return Icon(
        Icons.star,
        size: widget.size,
        color: activeColor,
      );
    } else if (widget.allowHalfRating && _currentRating >= starValue - 0.5) {
      return Stack(
        children: [
          Icon(
            Icons.star,
            size: widget.size,
            color: inactiveColor,
          ),
          ClipRect(
            clipper: HalfClipper(),
            child: Icon(
              Icons.star,
              size: widget.size,
              color: activeColor,
            ),
          ),
        ],
      );
    } else {
      return Icon(
        Icons.star_border,
        size: widget.size,
        color: inactiveColor,
      );
    }
  }
}

class HalfClipper extends CustomClipper<Rect> {
  @override
  Rect getClip(Size size) {
    return Rect.fromLTWH(0, 0, size.width / 2, size.height);
  }

  @override
  bool shouldReclip(CustomClipper<Rect> oldClipper) => false;
}

class RatingDialog extends StatefulWidget {
  final String title;
  final String? subtitle;
  final double initialRating;
  final Function(double, String?)? onSubmit;

  const RatingDialog({
    super.key,
    required this.title,
    this.subtitle,
    this.initialRating = 0.0,
    this.onSubmit,
  });

  @override
  State<RatingDialog> createState() => _RatingDialogState();
}

class _RatingDialogState extends State<RatingDialog> {
  late double _rating;
  final _commentController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _rating = widget.initialRating;
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Text(
        widget.title,
        textAlign: TextAlign.center,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.subtitle != null) ...[
            Text(
              widget.subtitle!,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          // التقييم بالنجوم
          InteractiveRatingWidget(
            initialRating: _rating,
            size: 40,
            onRatingChanged: (rating) {
              setState(() {
                _rating = rating;
              });
            },
          ),
          
          const SizedBox(height: 8),
          
          // نص التقييم
          Text(
            _getRatingText(_rating),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: _getRatingColor(_rating),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // حقل التعليق
          TextField(
            controller: _commentController,
            maxLines: 3,
            decoration: const InputDecoration(
              labelText: 'تعليق (اختياري)',
              hintText: 'شاركنا رأيك...',
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _rating > 0 ? () {
            widget.onSubmit?.call(
              _rating,
              _commentController.text.trim().isEmpty 
                  ? null 
                  : _commentController.text.trim(),
            );
            Navigator.of(context).pop();
          } : null,
          child: const Text('إرسال'),
        ),
      ],
    );
  }

  String _getRatingText(double rating) {
    if (rating == 0) return 'اختر تقييمك';
    if (rating <= 1) return 'سيء جداً';
    if (rating <= 2) return 'سيء';
    if (rating <= 3) return 'متوسط';
    if (rating <= 4) return 'جيد';
    return 'ممتاز';
  }

  Color _getRatingColor(double rating) {
    if (rating == 0) return Colors.grey;
    if (rating <= 2) return Colors.red;
    if (rating <= 3) return Colors.orange;
    if (rating <= 4) return Colors.blue;
    return Colors.green;
  }

  static void show(
    BuildContext context, {
    required String title,
    String? subtitle,
    double initialRating = 0.0,
    Function(double, String?)? onSubmit,
  }) {
    showDialog(
      context: context,
      builder: (context) => RatingDialog(
        title: title,
        subtitle: subtitle,
        initialRating: initialRating,
        onSubmit: onSubmit,
      ),
    );
  }
}

class RatingBar extends StatelessWidget {
  final double rating;
  final int totalReviews;
  final Map<int, int> ratingDistribution;

  const RatingBar({
    super.key,
    required this.rating,
    required this.totalReviews,
    required this.ratingDistribution,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Column(
                children: [
                  Text(
                    rating.toStringAsFixed(1),
                    style: const TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  RatingWidget(
                    rating: rating,
                    size: 20,
                  ),
                  Text(
                    '$totalReviews تقييم',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 24),
              Expanded(
                child: Column(
                  children: List.generate(5, (index) {
                    final starCount = 5 - index;
                    final count = ratingDistribution[starCount] ?? 0;
                    final percentage = totalReviews > 0 ? count / totalReviews : 0.0;
                    
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Row(
                        children: [
                          Text(
                            '$starCount',
                            style: const TextStyle(fontSize: 12),
                          ),
                          const SizedBox(width: 4),
                          const Icon(Icons.star, size: 12, color: Colors.amber),
                          const SizedBox(width: 8),
                          Expanded(
                            child: LinearProgressIndicator(
                              value: percentage,
                              backgroundColor: Colors.grey[300],
                              valueColor: const AlwaysStoppedAnimation<Color>(Colors.amber),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '$count',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    );
                  }),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
